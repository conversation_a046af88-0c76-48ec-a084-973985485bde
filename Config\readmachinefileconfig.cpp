﻿#include "readmachinefileconfig.h"

#include <QDebug>
#include <QFile>

ReadMachineFileConfig::ReadMachineFileConfig() {}

ReadMachineFileConfig::~ReadMachineFileConfig() {}

void ReadMachineFileConfig::parseConfig( QString fileAddr )
{
    QFile file( fileAddr );  // 替换成你的Json文件路径

    if ( !file.open( QIODevice::ReadOnly ) )
    {
        // 文件打开失败
        qDebug() << "MachineFile Config Json Open Failed!";
        QMessageBox::warning( nullptr, "错误提示", "MachineFileConfig.json文件不存在" );
        return;
    }

    // 读取文件内容到QByteArray
    QByteArray jsonBytes = file.readAll();
    // 将QByteArray解析为QJsonDocument
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    // 确保Json文档有效
    if ( jsonDocument.isNull() )
    {
        // Json文档无效
        qDebug() << "Test Config Json文档无效!";
        return;
    }

    // 将QJsonDocument转换为QJsonObject
    QJsonObject jsonObject = jsonDocument.object();

    // 获取basicCfg数组
    QJsonArray basicArray = jsonObject[ "basic" ].toArray();
    // 遍历basicCfg数组
    for ( int i = 0; i < basicArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = basicArray.at( i ).toObject();

        int                   key = step[ "id" ].toInt();
        MachineFlieConfigItem item;
        item.title    = step[ "title" ].toString();
        item.value    = step[ "value" ].toInt();
        item.unit     = step[ "unit" ].toString();
        item.editable = step[ "editable" ].toBool();
        if ( step.contains( "candidate" ) )
        {
            QJsonArray candidateArray = step[ "candidate" ].toArray();
            for ( int j = 0; j < candidateArray.size(); ++j )
            {
                item.candidate.append( candidateArray.at( j ).toString() );
            }
        }

        // 对key和value进行处理
        this->_basicCfgList.insert( key, item );
    }

    // 获取needleCfg数组
    QJsonArray needleArray = jsonObject[ "needle" ].toArray();
    // 遍历needleCfg数组
    for ( int i = 0; i < needleArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = needleArray.at( i ).toObject();

        int                   key = step[ "id" ].toInt();
        MachineFlieConfigItem item;
        item.title    = step[ "title" ].toString();
        item.value    = step[ "value" ].toInt();
        item.unit     = step[ "unit" ].toString();
        item.editable = step[ "editable" ].toBool();
        if ( step.contains( "candidate" ) )
        {
            QJsonArray candidateArray = step[ "candidate" ].toArray();
            for ( int j = 0; j < candidateArray.size(); ++j )
            {
                item.candidate.append( candidateArray.at( j ).toString() );
            }
        }

        // 对key和value进行处理
        this->_needleCfgList.insert( key, item );
    }

    // 获取peripheralCfg数组
    QJsonArray peripheralArray = jsonObject[ "peripheral" ].toArray();
    // 遍历peripheralCfg数组
    for ( int i = 0; i < peripheralArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = peripheralArray.at( i ).toObject();

        int                   key = step[ "id" ].toInt();
        MachineFlieConfigItem item;
        item.title    = step[ "title" ].toString();
        item.value    = step[ "value" ].toInt();
        item.unit     = step[ "unit" ].toString();
        item.editable = step[ "editable" ].toBool();
        if ( step.contains( "candidate" ) )
        {
            QJsonArray candidateArray = step[ "candidate" ].toArray();
            for ( int j = 0; j < candidateArray.size(); ++j )
            {
                item.candidate.append( candidateArray.at( j ).toString() );
            }
        }

        // 对key和value进行处理
        this->_peripheralCfgList.insert( key, item );
    }

    // 获取positionCfg数组
    QJsonArray positionArray = jsonObject[ "position" ].toArray();
    // 遍历positionCfg数组
    for ( int i = 0; i < positionArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = positionArray.at( i ).toObject();

        int                   key = step[ "id" ].toInt();
        MachineFlieConfigItem item;
        item.title    = step[ "title" ].toString();
        item.value    = step[ "value" ].toInt();
        item.unit     = step[ "unit" ].toString();
        item.editable = step[ "editable" ].toBool();
        if ( step.contains( "candidate" ) )
        {
            QJsonArray candidateArray = step[ "candidate" ].toArray();
            for ( int j = 0; j < candidateArray.size(); ++j )
            {
                item.candidate.append( candidateArray.at( j ).toString() );
            }
        }

        // 对key和value进行处理
        this->_positionCfgList.insert( key, item );
    }

    // 获取position2Cfg数组
    QJsonArray position2Array = jsonObject[ "position2" ].toArray();
    // 遍历positionCfg数组
    for ( int i = 0; i < position2Array.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = position2Array.at( i ).toObject();

        int                   key = step[ "id" ].toInt();
        MachineFlieConfigItem item;
        item.title    = step[ "title" ].toString();
        item.value    = step[ "value" ].toInt();
        item.unit     = step[ "unit" ].toString();
        item.editable = step[ "editable" ].toBool();
        if ( step.contains( "candidate" ) )
        {
            QJsonArray candidateArray = step[ "candidate" ].toArray();
            for ( int j = 0; j < candidateArray.size(); ++j )
            {
                item.candidate.append( candidateArray.at( j ).toString() );
            }
        }

        // 对key和value进行处理
        this->_position2CfgList.insert( key, item );
    }

    // 获取userCfg数组
    QJsonArray userArray = jsonObject[ "user" ].toArray();
    // 遍历userCfg数组
    for ( int i = 0; i < userArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = userArray.at( i ).toObject();

        int                   key = step[ "id" ].toInt();
        MachineFlieConfigItem item;
        item.title    = step[ "title" ].toString();
        item.value    = step[ "value" ].toInt();
        item.unit     = step[ "unit" ].toString();
        item.editable = step[ "editable" ].toBool();
        if ( step.contains( "candidate" ) )
        {
            QJsonArray candidateArray = step[ "candidate" ].toArray();
            for ( int j = 0; j < candidateArray.size(); ++j )
            {
                item.candidate.append( candidateArray.at( j ).toString() );
            }
        }

        // 对key和value进行处理
        this->_userCfgList.insert( key, item );
    }

    // 获取fengtouCfg数组
    QJsonArray fengtouArray = jsonObject[ "fengtou" ].toArray();
    // 遍历fengtouCfg数组
    for ( int i = 0; i < fengtouArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = fengtouArray.at( i ).toObject();

        int                   key = step[ "id" ].toInt();
        MachineFlieConfigItem item;
        item.title    = step[ "title" ].toString();
        item.value    = step[ "value" ].toInt();
        item.unit     = step[ "unit" ].toString();
        item.editable = step[ "editable" ].toBool();
        if ( step.contains( "candidate" ) )
        {
            QJsonArray candidateArray = step[ "candidate" ].toArray();
            for ( int j = 0; j < candidateArray.size(); ++j )
            {
                item.candidate.append( candidateArray.at( j ).toString() );
            }
        }

        // 对key和value进行处理
        this->_fengtouCfgList.insert( key, item );
    }

    // 获取otherCfg数组
    QJsonArray otherArray = jsonObject[ "other" ].toArray();
    // 遍历otherCfg数组
    for ( int i = 0; i < otherArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = otherArray.at( i ).toObject();

        int                   key = step[ "id" ].toInt();
        MachineFlieConfigItem item;
        item.title    = step[ "title" ].toString();
        item.value    = step[ "value" ].toInt();
        item.unit     = step[ "unit" ].toString();
        item.editable = step[ "editable" ].toBool();
        if ( step.contains( "candidate" ) )
        {
            QJsonArray candidateArray = step[ "candidate" ].toArray();
            for ( int j = 0; j < candidateArray.size(); ++j )
            {
                item.candidate.append( candidateArray.at( j ).toString() );
            }
        }

        // 对key和value进行处理
        this->_otherCfgList.insert( key, item );
    }

    // 获取socketMotorCfg数组
    QJsonArray socketMotorArray = jsonObject[ "socketMotor" ].toArray();
    // 遍历socketMotorCfg数组
    for ( int i = 0; i < socketMotorArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = socketMotorArray.at( i ).toObject();

        int                    key = step[ "id" ].toInt();
        StepperMotorConfigItem item;
        item.title = step[ "title" ].toString();

        QJsonArray dataArray = step[ "data" ].toArray();
        for ( int j = 0; j < dataArray.size(); ++j )
        {
            QJsonObject dataItem = dataArray.at( j ).toObject();

            StepperMotorConfigDataItem itemx;
            itemx.value    = dataItem[ "value" ].toInt();
            itemx.unit     = dataItem[ "unit" ].toString();
            itemx.editable = dataItem[ "editable" ].toBool();
            if ( dataItem.contains( "candidate" ) )
            {
                QJsonArray candidateArray = dataItem[ "candidate" ].toArray();
                for ( int k = 0; k < candidateArray.size(); ++k )
                {
                    itemx.candidate.append( candidateArray.at( k ).toString() );
                }
            }
            item.data.append( itemx );
        }

        // 对key和value进行处理
        this->_socketMotorCfgList.insert( key, item );
    }

    // 获取fengtouMotorCfg数组
    QJsonArray fengtouMotorArray = jsonObject[ "fengtouMotor" ].toArray();
    // 遍历fengtouMotorCfg数组
    for ( int i = 0; i < fengtouMotorArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = fengtouMotorArray.at( i ).toObject();

        int                    key = step[ "id" ].toInt();
        StepperMotorConfigItem item;
        item.title = step[ "title" ].toString();

        QJsonArray dataArray = step[ "data" ].toArray();
        for ( int j = 0; j < dataArray.size(); ++j )
        {
            QJsonObject dataItem = dataArray.at( j ).toObject();

            StepperMotorConfigDataItem itemx;
            itemx.value    = dataItem[ "value" ].toInt();
            itemx.unit     = dataItem[ "unit" ].toString();
            itemx.editable = dataItem[ "editable" ].toBool();
            if ( dataItem.contains( "candidate" ) )
            {
                QJsonArray candidateArray = dataItem[ "candidate" ].toArray();
                for ( int k = 0; k < candidateArray.size(); ++k )
                {
                    itemx.candidate.append( candidateArray.at( k ).toString() );
                }
            }
            item.data.append( itemx );
        }

        // 对key和value进行处理
        this->_fengtouMotorCfgList.insert( key, item );
    }

    // 获取otherMotorCfg数组
    QJsonArray otherMotorArray = jsonObject[ "otherMotor" ].toArray();
    // 遍历otherMotorCfg数组
    for ( int i = 0; i < otherMotorArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = otherMotorArray.at( i ).toObject();

        int                    key = step[ "id" ].toInt();
        StepperMotorConfigItem item;
        item.title = step[ "title" ].toString();

        QJsonArray dataArray = step[ "data" ].toArray();
        for ( int j = 0; j < dataArray.size(); ++j )
        {
            QJsonObject dataItem = dataArray.at( j ).toObject();

            StepperMotorConfigDataItem itemx;
            itemx.value    = dataItem[ "value" ].toInt();
            itemx.unit     = dataItem[ "unit" ].toString();
            itemx.editable = dataItem[ "editable" ].toBool();
            if ( dataItem.contains( "candidate" ) )
            {
                QJsonArray candidateArray = dataItem[ "candidate" ].toArray();
                for ( int k = 0; k < candidateArray.size(); ++k )
                {
                    itemx.candidate.append( candidateArray.at( k ).toString() );
                }
            }
            item.data.append( itemx );
        }

        // 对key和value进行处理
        this->_otherMotorCfgList.insert( key, item );
    }

    // 获取socketWarnCfg数组
    QJsonArray socketWarnArray = jsonObject[ "socketWarn" ].toArray();
    // 遍历socketWarnCfg数组
    for ( int i = 0; i < socketWarnArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = socketWarnArray.at( i ).toObject();

        int            key = step[ "id" ].toInt();
        WarnConfigItem item;
        item.title       = step[ "title" ].toString();
        item.enable      = step[ "enable" ].toBool();
        item.sensitivity = step[ "sensitivity" ].toInt();
        item.direction   = step[ "direction" ].toInt();

        // 对key和value进行处理
        this->_socketWarnCfgList.insert( key, item );
    }

    // 获取fengtouWarnCfg数组
    QJsonArray fengtouWarnArray = jsonObject[ "fengtouWarn" ].toArray();
    // 遍历fengtouWarnCfg数组
    for ( int i = 0; i < fengtouWarnArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = fengtouWarnArray.at( i ).toObject();

        int            key = step[ "id" ].toInt();
        WarnConfigItem item;
        item.title       = step[ "title" ].toString();
        item.enable      = step[ "enable" ].toBool();
        item.sensitivity = step[ "sensitivity" ].toInt();
        item.direction   = step[ "direction" ].toInt();

        // 对key和value进行处理
        this->_fengtouWarnCfgList.insert( key, item );
    }

    // 获取fastResetCfg数组
    QJsonArray fastResetArray = jsonObject[ "fastReset" ].toArray();
    // 遍历fastResetCfg数组
    for ( int i = 0; i < fastResetArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = fastResetArray.at( i ).toObject();

        int                 key = step[ "id" ].toInt();
        FastResetConfigItem item;
        item.step_name                     = step[ "step_name" ].toString();
        item.chicun                        = step[ "chicun" ].toInt();
        item.speed                         = step[ "speed" ].toInt();
        item.huaxing_shezhi                = step[ "huaxing_shezhi" ].toInt();
        item.lamao_shezhi                  = step[ "lamao_shezhi" ].toInt();
        item.dagen_zhongxin                = step[ "dagen_zhongxin" ].toInt();
        item.dagen_pianyi                  = step[ "dagen_pianyi" ].toInt();
        item.fujia                         = step[ "fujia" ].toInt();
        QJsonObject xiangjin               = step[ "xiangjin" ].toObject();
        item.xiangjin.start_value          = xiangjin[ "start_value" ].toInt();
        item.xiangjin.end_value            = xiangjin[ "end_value" ].toInt();
        item.xiangjin.state                = xiangjin[ "state" ].toInt();
        QJsonObject ktf1                   = step[ "ktf1" ].toObject();
        item.ktf1.start_value              = ktf1[ "start_value" ].toInt();
        item.ktf1.end_value                = ktf1[ "end_value" ].toInt();
        item.ktf1.state                    = ktf1[ "state" ].toInt();
        QJsonObject ktf2                   = step[ "ktf2" ].toObject();
        item.ktf2.start_value              = ktf2[ "start_value" ].toInt();
        item.ktf2.end_value                = ktf2[ "end_value" ].toInt();
        item.ktf2.state                    = ktf2[ "state" ].toInt();
        QJsonObject zhentong_Density       = step[ "zhentong_Density" ].toObject();
        item.zhentong_Density.start_value  = zhentong_Density[ "start_value" ].toInt();
        item.zhentong_Density.end_value    = zhentong_Density[ "end_value" ].toInt();
        item.zhentong_Density.state        = zhentong_Density[ "state" ].toInt();
        QJsonObject chenjiang_Density      = step[ "chenjiang_Density" ].toObject();
        item.chenjiang_Density.start_value = chenjiang_Density[ "start_value" ].toInt();
        item.chenjiang_Density.end_value   = chenjiang_Density[ "end_value" ].toInt();
        item.chenjiang_Density.state       = chenjiang_Density[ "state" ].toInt();
        QJsonObject shengke_Density        = step[ "shengke_Density" ].toObject();
        item.shengke_Density.start_value   = shengke_Density[ "start_value" ].toInt();
        item.shengke_Density.end_value     = shengke_Density[ "end_value" ].toInt();
        item.shengke_Density.state         = shengke_Density[ "state" ].toInt();
        QJsonObject zuo_lengjiao           = step[ "zuo_lengjiao" ].toObject();
        item.zuo_lengjiao.start_value      = zuo_lengjiao[ "start_value" ].toInt();
        item.zuo_lengjiao.end_value        = zuo_lengjiao[ "end_value" ].toInt();
        item.zuo_lengjiao.state            = zuo_lengjiao[ "state" ].toInt();
        QJsonObject you_lengjiao           = step[ "you_lengjiao" ].toObject();
        item.you_lengjiao.start_value      = you_lengjiao[ "start_value" ].toInt();
        item.you_lengjiao.end_value        = you_lengjiao[ "end_value" ].toInt();
        item.you_lengjiao.state            = you_lengjiao[ "state" ].toInt();

        QJsonArray actionArray = step[ "cy_action_msg" ].toArray();
        for ( int j = 0; j < actionArray.size(); ++j )
        {
            QJsonObject    actionObj = actionArray.at( j ).toObject();
            CYActionStruct action;
            action.cy_num        = actionObj[ "cy_num" ].toInt();
            action.cy_circle_cnt = actionObj[ "cy_circle_cnt" ].toInt();
            action.cy_state      = actionObj[ "cy_state" ].toInt();

            QJsonArray cmdArray = actionObj[ "cmd_msg" ].toArray();
            for ( int k = 0; k < cmdArray.size(); ++k )
            {
                QJsonObject cmdObj = cmdArray.at( k ).toObject();
                CmdStruct   cmd;
                cmd.cmd_num   = cmdObj[ "cmd_num" ].toInt();
                cmd.cmd_param = cmdObj[ "cmd_param" ].toInt();
                cmd.cmd_state = cmdObj[ "cmd_state" ].toInt();
                action.cmd_msg.append( cmd );
            }
            item.cy_action_msg.append( action );
        }

        // 对key和value进行处理
        this->_fastResetCfgList.insert( key, item );
    }

    // 获取fengtouCalCfg数组
    QJsonArray fengtouCalArray = jsonObject[ "fengtouCal" ].toArray();
    // 遍历fengtouCalCfg数组
    for ( int i = 0; i < fengtouCalArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = fengtouCalArray.at( i ).toObject();

        int               key = step[ "id" ].toInt();
        FengtouCalCfgItem item;
        QJsonArray        stepArray = step[ "step" ].toArray();
        for ( int j = 0; j < stepArray.size(); ++j )
        {
            QJsonObject       subStep = stepArray.at( j ).toObject();
            FengtouStepStruct subItem;
            subItem.baojing_yanshi  = subStep[ "baojing_yanshi" ].toInt();
            subItem.dongzuo_yanshi  = subStep[ "dongzuo_yanshi" ].toInt();
            subItem.dongzuo_zhiling = subStep[ "dongzuo_zhiling" ].toInt();
            subItem.fuwei_dongzuo   = subStep[ "fuwei_dongzuo" ].toInt();

            QJsonArray Valve_array = subStep[ "Valve_array" ].toArray();
            for ( int k = 0; k < Valve_array.size(); ++k )
            {
                QJsonObject              valveObj = Valve_array.at( k ).toObject();
                FengtouSignalValveStruct valve;
                valve.num   = valveObj[ "num" ].toInt();
                valve.type  = valveObj[ "type" ].toInt();
                valve.state = valveObj[ "state" ].toInt();
                subItem.Valve_array.append( valve );
            }
            QJsonArray motor_array = subStep[ "motor_array" ].toArray();
            for ( int k = 0; k < motor_array.size(); ++k )
            {
                QJsonObject        motorObj = motor_array.at( k ).toObject();
                FengtouMotorStruct motor;
                motor.num     = motorObj[ "num" ].toInt();
                motor.pattern = motorObj[ "pattern" ].toInt();
                motor.val     = motorObj[ "val" ].toInt();
                subItem.motor_array.append( motor );
            }
            item.step.append( subItem );
        }

        // 对key和value进行处理
        this->_fengtouCalCfgList.insert( key, item );
    }

    // 获取zeroSensor数组
    QJsonArray zeroSensorArray = jsonObject[ "zeroSensor" ].toArray();
    // 遍历zeroSensorCfg数组
    for ( int i = 0; i < zeroSensorArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject step = zeroSensorArray.at( i ).toObject();

        int     key  = step[ "id" ].toInt();
        QString name = step[ "name" ].toString();

        // 对key和value进行处理
        this->_zeroSensorList.insert( key, name );
    }

    // 获取ServoCfg数组
    QJsonArray servoArray = jsonObject[ "Servo" ].toArray();
    // 遍历ServoCfg数组
    for ( int i = 0; i < servoArray.size(); ++i )
    {
        // 获取数组元素
        QJsonObject servo = servoArray.at( i ).toObject();

        int          key = servo[ "id" ].toInt();
        ServoCfgItem item;
        item.id       = servo[ "id" ].toInt();
        item.title    = servo[ "title" ].toString();
        item.value    = servo[ "value" ].toInt();
        item.unit     = servo[ "unit" ].toString();
        item.editable = servo[ "editable" ].toBool();

        // 对key和value进行处理
        this->_servoList.insert( key, item );
    }

    // 解析EEye字段
    // 解析EEye配置
    if ( jsonObject.contains( "EEye" ) )
    {
        QJsonObject eeyeObj = jsonObject[ "EEye" ].toObject();

        // 获取realCount
        _eeyeCfg.realCount = eeyeObj[ "realCount" ].toInt();

        // 获取items数组
        QJsonArray itemsArray = eeyeObj[ "items" ].toArray();

        // 清空现有items
        _eeyeCfg.items.clear();

        // 遍历items数组
        for ( int i = 0; i < itemsArray.size(); ++i )
        {
            QJsonObject itemObj = itemsArray[ i ].toObject();

            EEyeItemStruct item;
            item.id              = itemObj[ "id" ].toInt();
            item.sensitivity     = itemObj[ "sensitivity" ].toInt();
            item.inductionDelay  = itemObj[ "inductionDelay" ].toInt();
            item.stFreq          = itemObj[ "stFreq" ].toInt();
            item.endFreq         = itemObj[ "endFreq" ].toInt();
            item.mappedId        = itemObj[ "mappedId" ].toInt();
            item.disConThreshold = itemObj[ "disConThreshold" ].toInt();
            item.wrapThreshold   = itemObj[ "wrapThreshold" ].toInt();

            _eeyeCfg.items.append( item );
        }
    }

    file.close();  // 关闭文件
}

quint8 ReadMachineFileConfig::saveConfig( QString fileAddr )
{
    QFile file( fileAddr );  // 替换成你的Json文件路径

    if ( !file.open( QIODevice::WriteOnly ) )
    {
        // 文件打开失败
        qDebug() << "MachineFile Config Json Open Failed";
        return -1;
    }

    QJsonObject jsonObjectMain;

    try
    {
        QJsonArray basicArray;
        // 遍历basicCfg数组
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_basicCfgList.begin(); it != this->_basicCfgList.end(); ++it )
        {
            // 获取数组元素
            MachineFlieConfigItem& item = it.value();
            QJsonObject            step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            step.insert( "value", item.value );
            step.insert( "unit", item.unit );
            step.insert( "editable", item.editable );
            if ( item.unit == "enum" )
            {
                QJsonArray candidateArray;
                for ( QVector< QString >::iterator itx = item.candidate.begin(); itx != item.candidate.end(); ++itx )
                {
                    QJsonValue jsonValue( *itx );
                    candidateArray.append( jsonValue );
                }
                step.insert( "candidate", candidateArray );
            }
            basicArray.append( step );
        }
        jsonObjectMain.insert( "basic", basicArray );

        QJsonArray needleArray;
        // 遍历needleCfg数组
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_needleCfgList.begin(); it != this->_needleCfgList.end(); ++it )
        {
            // 获取数组元素
            MachineFlieConfigItem& item = it.value();
            QJsonObject            step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            step.insert( "value", item.value );
            step.insert( "unit", item.unit );
            step.insert( "editable", item.editable );
            if ( item.unit == "enum" )
            {
                QJsonArray candidateArray;
                for ( QVector< QString >::iterator itx = item.candidate.begin(); itx != item.candidate.end(); ++itx )
                {
                    QJsonValue jsonValue( *itx );
                    candidateArray.append( jsonValue );
                }
                step.insert( "candidate", candidateArray );
            }
            needleArray.append( step );
        }
        jsonObjectMain.insert( "needle", needleArray );

        QJsonArray peripheralArray;
        // 遍历peripheralCfg数组
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_peripheralCfgList.begin(); it != this->_peripheralCfgList.end(); ++it )
        {
            // 获取数组元素
            MachineFlieConfigItem& item = it.value();
            QJsonObject            step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            step.insert( "value", item.value );
            step.insert( "unit", item.unit );
            step.insert( "editable", item.editable );
            if ( item.unit == "enum" )
            {
                QJsonArray candidateArray;
                for ( QVector< QString >::iterator itx = item.candidate.begin(); itx != item.candidate.end(); ++itx )
                {
                    QJsonValue jsonValue( *itx );
                    candidateArray.append( jsonValue );
                }
                step.insert( "candidate", candidateArray );
            }
            peripheralArray.append( step );
        }
        jsonObjectMain.insert( "peripheral", peripheralArray );

        QJsonArray positionArray;
        // 遍历position数组
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_positionCfgList.begin(); it != this->_positionCfgList.end(); ++it )
        {
            // 获取数组元素
            MachineFlieConfigItem& item = it.value();
            QJsonObject            step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            step.insert( "value", item.value );
            step.insert( "unit", item.unit );
            step.insert( "editable", item.editable );
            if ( item.unit == "enum" )
            {
                QJsonArray candidateArray;
                for ( QVector< QString >::iterator itx = item.candidate.begin(); itx != item.candidate.end(); ++itx )
                {
                    QJsonValue jsonValue( *itx );
                    candidateArray.append( jsonValue );
                }
                step.insert( "candidate", candidateArray );
            }
            positionArray.append( step );
        }
        jsonObjectMain.insert( "position", positionArray );

        QJsonArray position2Array;
        // 遍历position2数组
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_position2CfgList.begin(); it != this->_position2CfgList.end(); ++it )
        {
            // 获取数组元素
            MachineFlieConfigItem& item = it.value();
            QJsonObject            step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            step.insert( "value", item.value );
            step.insert( "unit", item.unit );
            step.insert( "editable", item.editable );
            if ( item.unit == "enum" )
            {
                QJsonArray candidateArray;
                for ( QVector< QString >::iterator itx = item.candidate.begin(); itx != item.candidate.end(); ++itx )
                {
                    QJsonValue jsonValue( *itx );
                    candidateArray.append( jsonValue );
                }
                step.insert( "candidate", candidateArray );
            }
            position2Array.append( step );
        }
        jsonObjectMain.insert( "position2", position2Array );

        QJsonArray userArray;
        // 遍历userCfg数组
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_userCfgList.begin(); it != this->_userCfgList.end(); ++it )
        {
            // 获取数组元素
            MachineFlieConfigItem& item = it.value();
            QJsonObject            step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            step.insert( "value", item.value );
            step.insert( "unit", item.unit );
            step.insert( "editable", item.editable );
            if ( item.unit == "enum" )
            {
                QJsonArray candidateArray;
                for ( QVector< QString >::iterator itx = item.candidate.begin(); itx != item.candidate.end(); ++itx )
                {
                    QJsonValue jsonValue( *itx );
                    candidateArray.append( jsonValue );
                }
                step.insert( "candidate", candidateArray );
            }
            userArray.append( step );
        }
        jsonObjectMain.insert( "user", userArray );

        QJsonArray fengtouArray;
        // 遍历fengtouCfg数组
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_fengtouCfgList.begin(); it != this->_fengtouCfgList.end(); ++it )
        {
            // 获取数组元素
            MachineFlieConfigItem& item = it.value();
            QJsonObject            step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            step.insert( "value", item.value );
            step.insert( "unit", item.unit );
            step.insert( "editable", item.editable );
            if ( item.unit == "enum" )
            {
                QJsonArray candidateArray;
                for ( QVector< QString >::iterator itx = item.candidate.begin(); itx != item.candidate.end(); ++itx )
                {
                    QJsonValue jsonValue( *itx );
                    candidateArray.append( jsonValue );
                }
                step.insert( "candidate", candidateArray );
            }
            fengtouArray.append( step );
        }
        jsonObjectMain.insert( "fengtou", fengtouArray );

        QJsonArray otherArray;
        // 遍历otherCfg数组
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_otherCfgList.begin(); it != this->_otherCfgList.end(); ++it )
        {
            // 获取数组元素
            MachineFlieConfigItem& item = it.value();
            QJsonObject            step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            step.insert( "value", item.value );
            step.insert( "unit", item.unit );
            step.insert( "editable", item.editable );
            if ( item.unit == "enum" )
            {
                QJsonArray candidateArray;
                for ( QVector< QString >::iterator itx = item.candidate.begin(); itx != item.candidate.end(); ++itx )
                {
                    QJsonValue jsonValue( *itx );
                    candidateArray.append( jsonValue );
                }
                step.insert( "candidate", candidateArray );
            }
            otherArray.append( step );
        }
        jsonObjectMain.insert( "other", otherArray );

        QJsonArray socketMotorArray;
        // 遍历socketMotorCfg数组
        for ( QMap< int, StepperMotorConfigItem >::iterator it = this->_socketMotorCfgList.begin(); it != this->_socketMotorCfgList.end(); ++it )
        {
            // 获取数组元素
            StepperMotorConfigItem& item = it.value();
            QJsonObject             step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            QJsonArray dataArray;
            for ( QVector< StepperMotorConfigDataItem >::iterator itx = item.data.begin(); itx != item.data.end(); ++itx )
            {
                QJsonObject data;
                if ( itx->unit == "/" )
                {
                    data.insert( "value", "null" );
                }
                else
                {
                    data.insert( "value", itx->value );
                }
                data.insert( "unit", itx->unit );
                data.insert( "editable", itx->editable );
                if ( itx->unit == "enum" )
                {
                    QJsonArray candidateArray;
                    for ( QVector< QString >::iterator ity = itx->candidate.begin(); ity != itx->candidate.end(); ++ity )
                    {
                        QJsonValue jsonValue( *ity );
                        candidateArray.append( jsonValue );
                    }
                    data.insert( "candidate", candidateArray );
                }
                dataArray.append( data );
            }
            step.insert( "data", dataArray );
            socketMotorArray.append( step );
        }
        jsonObjectMain.insert( "socketMotor", socketMotorArray );

        QJsonArray fengtouMotorArray;
        // 遍历fengtouMotorCfg数组
        for ( QMap< int, StepperMotorConfigItem >::iterator it = this->_fengtouMotorCfgList.begin(); it != this->_fengtouMotorCfgList.end(); ++it )
        {
            // 获取数组元素
            StepperMotorConfigItem& item = it.value();
            QJsonObject             step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            QJsonArray dataArray;
            for ( QVector< StepperMotorConfigDataItem >::iterator itx = item.data.begin(); itx != item.data.end(); ++itx )
            {
                QJsonObject data;
                if ( itx->unit == "/" )
                {
                    data.insert( "value", "null" );
                }
                else
                {
                    data.insert( "value", itx->value );
                }
                data.insert( "unit", itx->unit );
                data.insert( "editable", itx->editable );
                if ( itx->unit == "enum" )
                {
                    QJsonArray candidateArray;
                    for ( QVector< QString >::iterator ity = itx->candidate.begin(); ity != itx->candidate.end(); ++ity )
                    {
                        QJsonValue jsonValue( *ity );
                        candidateArray.append( jsonValue );
                    }
                    data.insert( "candidate", candidateArray );
                }
                dataArray.append( data );
            }
            step.insert( "data", dataArray );
            fengtouMotorArray.append( step );
        }
        jsonObjectMain.insert( "fengtouMotor", fengtouMotorArray );

        QJsonArray otherMotorArray;
        // 遍历otherMotorCfg数组
        for ( QMap< int, StepperMotorConfigItem >::iterator it = this->_otherMotorCfgList.begin(); it != this->_otherMotorCfgList.end(); ++it )
        {
            // 获取数组元素
            StepperMotorConfigItem& item = it.value();
            QJsonObject             step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            QJsonArray dataArray;
            for ( QVector< StepperMotorConfigDataItem >::iterator itx = item.data.begin(); itx != item.data.end(); ++itx )
            {
                QJsonObject data;
                if ( itx->unit == "/" )
                {
                    data.insert( "value", "null" );
                }
                else
                {
                    data.insert( "value", itx->value );
                }
                data.insert( "unit", itx->unit );
                data.insert( "editable", itx->editable );
                if ( itx->unit == "enum" )
                {
                    QJsonArray candidateArray;
                    for ( QVector< QString >::iterator ity = itx->candidate.begin(); ity != itx->candidate.end(); ++ity )
                    {
                        QJsonValue jsonValue( *ity );
                        candidateArray.append( jsonValue );
                    }
                    data.insert( "candidate", candidateArray );
                }
                dataArray.append( data );
            }
            step.insert( "data", dataArray );
            otherMotorArray.append( step );
        }
        jsonObjectMain.insert( "otherMotor", otherMotorArray );

        QJsonArray socketWarnArray;
        // 遍历socketWarnCfg数组
        for ( QMap< int, WarnConfigItem >::iterator it = this->_socketWarnCfgList.begin(); it != this->_socketWarnCfgList.end(); ++it )
        {
            // 获取数组元素
            WarnConfigItem& item = it.value();
            QJsonObject     step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            step.insert( "enable", item.enable );
            step.insert( "sensitivity", item.sensitivity );
            step.insert( "direction", item.direction );
            socketWarnArray.append( step );
        }
        jsonObjectMain.insert( "socketWarn", socketWarnArray );

        QJsonArray fengtouWarnArray;
        // 遍历fengtouWarnCfg数组
        for ( QMap< int, WarnConfigItem >::iterator it = this->_fengtouWarnCfgList.begin(); it != this->_fengtouWarnCfgList.end(); ++it )
        {
            // 获取数组元素
            WarnConfigItem& item = it.value();
            QJsonObject     step;
            step.insert( "id", it.key() );
            step.insert( "title", item.title );
            step.insert( "enable", item.enable );
            step.insert( "sensitivity", item.sensitivity );
            step.insert( "direction", item.direction );
            fengtouWarnArray.append( step );
        }
        jsonObjectMain.insert( "fengtouWarn", fengtouWarnArray );

        QJsonArray fastResetArray;
        // 遍历fastResetCfg数组
        for ( QMap< int, FastResetConfigItem >::iterator it = this->_fastResetCfgList.begin(); it != this->_fastResetCfgList.end(); ++it )
        {
            // 获取数组元素
            FastResetConfigItem& item = it.value();
            QJsonObject          step;
            step.insert( "id", it.key() );
            step.insert( "step_name", item.step_name );
            step.insert( "chicun", item.chicun );
            step.insert( "speed", item.speed );
            step.insert( "huaxing_shezhi", item.huaxing_shezhi );
            step.insert( "lamao_shezhi", item.lamao_shezhi );
            step.insert( "dagen_zhongxin", item.dagen_zhongxin );
            step.insert( "dagen_pianyi", item.dagen_pianyi );
            step.insert( "fujia", item.fujia );
            QJsonObject xiangjin;
            xiangjin.insert( "start_value", item.xiangjin.start_value );
            xiangjin.insert( "end_value", item.xiangjin.end_value );
            xiangjin.insert( "state", item.xiangjin.state );
            step.insert( "xiangjin", xiangjin );
            QJsonObject ktf1;
            ktf1.insert( "start_value", item.ktf1.start_value );
            ktf1.insert( "end_value", item.ktf1.end_value );
            ktf1.insert( "state", item.ktf1.state );
            step.insert( "ktf1", ktf1 );
            QJsonObject ktf2;
            ktf2.insert( "start_value", item.ktf2.start_value );
            ktf2.insert( "end_value", item.ktf2.end_value );
            ktf2.insert( "state", item.ktf2.state );
            step.insert( "ktf2", ktf2 );
            QJsonObject zhentong_Density;
            zhentong_Density.insert( "start_value", item.zhentong_Density.start_value );
            zhentong_Density.insert( "end_value", item.zhentong_Density.end_value );
            zhentong_Density.insert( "state", item.zhentong_Density.state );
            step.insert( "zhentong_Density", zhentong_Density );
            QJsonObject chenjiang_Density;
            chenjiang_Density.insert( "start_value", item.chenjiang_Density.start_value );
            chenjiang_Density.insert( "end_value", item.chenjiang_Density.end_value );
            chenjiang_Density.insert( "state", item.chenjiang_Density.state );
            step.insert( "chenjiang_Density", chenjiang_Density );
            QJsonObject shengke_Density;
            shengke_Density.insert( "start_value", item.shengke_Density.start_value );
            shengke_Density.insert( "end_value", item.shengke_Density.end_value );
            shengke_Density.insert( "state", item.shengke_Density.state );
            step.insert( "shengke_Density", shengke_Density );
            QJsonObject zuo_lengjiao;
            zuo_lengjiao.insert( "start_value", item.zuo_lengjiao.start_value );
            zuo_lengjiao.insert( "end_value", item.zuo_lengjiao.end_value );
            zuo_lengjiao.insert( "state", item.zuo_lengjiao.state );
            step.insert( "zuo_lengjiao", zuo_lengjiao );
            QJsonObject you_lengjiao;
            you_lengjiao.insert( "start_value", item.you_lengjiao.start_value );
            you_lengjiao.insert( "end_value", item.you_lengjiao.end_value );
            you_lengjiao.insert( "state", item.you_lengjiao.state );
            step.insert( "you_lengjiao", you_lengjiao );

            QJsonArray cy_action_msgArray;
            for ( QVector< CYActionStruct >::iterator it1 = item.cy_action_msg.begin(); it1 != item.cy_action_msg.end(); ++it1 )
            {
                QJsonObject cy_action_msg;
                cy_action_msg.insert( "cy_num", it1->cy_num );
                cy_action_msg.insert( "cy_circle_cnt", it1->cy_circle_cnt );
                cy_action_msg.insert( "cy_state", it1->cy_state );
                QJsonArray cmd_msgArray;
                for ( QVector< CmdStruct >::iterator it2 = it1->cmd_msg.begin(); it2 != it1->cmd_msg.end(); ++it2 )
                {
                    QJsonObject cmd_msg;
                    cmd_msg.insert( "cmd_num", it2->cmd_num );
                    cmd_msg.insert( "cmd_param", it2->cmd_param );
                    cmd_msg.insert( "cmd_state", it2->cmd_state );
                    cmd_msgArray.append( cmd_msg );
                }
                cy_action_msg.insert( "cmd_msg", cmd_msgArray );
                cy_action_msgArray.append( cy_action_msg );
            }
            step.insert( "cy_action_msg", cy_action_msgArray );

            fastResetArray.append( step );
        }
        jsonObjectMain.insert( "fastReset", fastResetArray );

        QJsonArray fengtouCalArray;
        // 遍历fengtouCalCfg数组
        for ( QMap< int, FengtouCalCfgItem >::iterator it = this->_fengtouCalCfgList.begin(); it != this->_fengtouCalCfgList.end(); ++it )
        {
            // 获取数组元素
            FengtouCalCfgItem& item = it.value();
            QJsonObject        step;
            step.insert( "id", it.key() );
            QJsonArray stepArray;
            for ( QVector< FengtouStepStruct >::iterator it1 = item.step.begin(); it1 != item.step.end(); ++it1 )
            {
                QJsonObject subStep;
                subStep.insert( "baojing_yanshi", it1->baojing_yanshi );
                subStep.insert( "dongzuo_yanshi", it1->dongzuo_yanshi );
                subStep.insert( "dongzuo_zhiling", it1->dongzuo_zhiling );
                subStep.insert( "fuwei_dongzuo", it1->fuwei_dongzuo );
                QJsonArray Valve_array;
                for ( QVector< FengtouSignalValveStruct >::iterator it2 = it1->Valve_array.begin(); it2 != it1->Valve_array.end(); ++it2 )
                {
                    QJsonObject valve;
                    valve.insert( "num", it2->num );
                    valve.insert( "type", it2->type );
                    valve.insert( "state", it2->state );
                    Valve_array.append( valve );
                }
                subStep.insert( "Valve_array", Valve_array );
                QJsonArray motor_array;
                for ( QVector< FengtouMotorStruct >::iterator it3 = it1->motor_array.begin(); it3 != it1->motor_array.end(); ++it3 )
                {
                    QJsonObject motor;
                    motor.insert( "num", it3->num );
                    motor.insert( "pattern", it3->pattern );
                    motor.insert( "val", it3->val );
                    motor_array.append( motor );
                }
                subStep.insert( "motor_array", motor_array );
                stepArray.append( subStep );
            }
            step.insert( "step", stepArray );
            fengtouCalArray.append( step );
        }
        jsonObjectMain.insert( "fengtouCal", fengtouCalArray );

        QJsonArray zeroSensorArray;
        // 遍历zeroSensorCfg数组
        for ( QMap< int, QString >::iterator it = this->_zeroSensorList.begin(); it != this->_zeroSensorList.end(); ++it )
        {
            // 获取数组元素
            QJsonObject step;
            step.insert( "id", it.key() );
            step.insert( "name", it.value() );
            zeroSensorArray.append( step );
        }
        jsonObjectMain.insert( "zeroSensor", zeroSensorArray );

        // 保存到文件
        QJsonArray servoArray;
        // 遍历servoCfg数组
        for ( QMap< int, ServoCfgItem >::iterator it = this->_servoList.begin(); it != this->_servoList.end(); ++it )
        {
            // 获取数组元素
            ServoCfgItem& item = it.value();
            QJsonObject   servo;
            servo.insert( "id", item.id );
            servo.insert( "title", item.title );
            servo.insert( "value", item.value );
            servo.insert( "unit", item.unit );
            servo.insert( "editable", item.editable );
            servoArray.append( servo );
        }
        jsonObjectMain.insert( "Servo", servoArray );

        // 保存EEye配置
        QJsonObject eeyeObj;
        eeyeObj[ "realCount" ] = _eeyeCfg.realCount;

        QJsonArray itemsArray;
        for ( int i = 0; i < _eeyeCfg.items.size(); ++i )
        {
            QJsonObject itemObj;
            itemObj[ "id" ]              = _eeyeCfg.items[ i ].id;
            itemObj[ "sensitivity" ]     = _eeyeCfg.items[ i ].sensitivity;
            itemObj[ "inductionDelay" ]  = _eeyeCfg.items[ i ].inductionDelay;
            itemObj[ "stFreq" ]          = _eeyeCfg.items[ i ].stFreq;
            itemObj[ "endFreq" ]         = _eeyeCfg.items[ i ].endFreq;
            itemObj[ "mappedId" ]        = _eeyeCfg.items[ i ].mappedId;
            itemObj[ "disConThreshold" ] = _eeyeCfg.items[ i ].disConThreshold;
            itemObj[ "wrapThreshold" ]   = _eeyeCfg.items[ i ].wrapThreshold;

            itemsArray.append( itemObj );
        }

        eeyeObj[ "items" ]       = itemsArray;
        jsonObjectMain[ "EEye" ] = eeyeObj;

        QJsonDocument jsonDocument( jsonObjectMain );
        file.write( jsonDocument.toJson() );
        qDebug() << "JSON 文件写入成功";
        file.close();  // 关闭文件
        return 1;
    }
    catch ( const std::exception& e )
    {
        file.close();  // 关闭文件
        return -1;
    }
}

// 只保存机器相关配置到文件
quint8 ReadMachineFileConfig::saveMachineConfig( QString fileAddr )
{
    QFile file( fileAddr );

    // 首先读取现有文件内容
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        qDebug() << "无法打开文件进行读取:" << fileAddr;
        return -1;
    }

    // 读取文件内容到QByteArray
    QByteArray jsonBytes = file.readAll();
    file.close();

    // 将QByteArray解析为QJsonDocument
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    // 确保Json文档有效
    if ( jsonDocument.isNull() )
    {
        qDebug() << "JSON文档无效!";
        return -1;
    }

    // 将QJsonDocument转换为QJsonObject
    QJsonObject jsonObjectMain = jsonDocument.object();

    try
    {
        // 更新basic部分
        QJsonArray basicArray;
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_basicCfgList.begin(); it != this->_basicCfgList.end(); ++it )
        {
            QJsonObject step;
            step.insert( "id", it.key() );
            step.insert( "title", it.value().title );
            step.insert( "value", it.value().value );
            step.insert( "unit", it.value().unit );
            step.insert( "editable", it.value().editable );

            if ( !it.value().candidate.isEmpty() )
            {
                QJsonArray candidateArray;
                for ( int i = 0; i < it.value().candidate.size(); i++ )
                {
                    candidateArray.append( it.value().candidate[ i ] );
                }
                step.insert( "candidate", candidateArray );
            }

            basicArray.append( step );
        }
        jsonObjectMain[ "basic" ] = basicArray;

        // 更新needle部分
        QJsonArray needleArray;
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_needleCfgList.begin(); it != this->_needleCfgList.end(); ++it )
        {
            QJsonObject step;
            step.insert( "id", it.key() );
            step.insert( "title", it.value().title );
            step.insert( "value", it.value().value );
            step.insert( "unit", it.value().unit );
            step.insert( "editable", it.value().editable );

            if ( !it.value().candidate.isEmpty() )
            {
                QJsonArray candidateArray;
                for ( int i = 0; i < it.value().candidate.size(); i++ )
                {
                    candidateArray.append( it.value().candidate[ i ] );
                }
                step.insert( "candidate", candidateArray );
            }

            needleArray.append( step );
        }
        jsonObjectMain[ "needle" ] = needleArray;

        // 更新peripheral部分
        QJsonArray peripheralArray;
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_peripheralCfgList.begin(); it != this->_peripheralCfgList.end(); ++it )
        {
            QJsonObject step;
            step.insert( "id", it.key() );
            step.insert( "title", it.value().title );
            step.insert( "value", it.value().value );
            step.insert( "unit", it.value().unit );
            step.insert( "editable", it.value().editable );

            if ( !it.value().candidate.isEmpty() )
            {
                QJsonArray candidateArray;
                for ( int i = 0; i < it.value().candidate.size(); i++ )
                {
                    candidateArray.append( it.value().candidate[ i ] );
                }
                step.insert( "candidate", candidateArray );
            }

            peripheralArray.append( step );
        }
        jsonObjectMain[ "peripheral" ] = peripheralArray;

        // 更新position部分
        QJsonArray positionArray;
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_positionCfgList.begin(); it != this->_positionCfgList.end(); ++it )
        {
            QJsonObject step;
            step.insert( "id", it.key() );
            step.insert( "title", it.value().title );
            step.insert( "value", it.value().value );
            step.insert( "unit", it.value().unit );
            step.insert( "editable", it.value().editable );

            if ( !it.value().candidate.isEmpty() )
            {
                QJsonArray candidateArray;
                for ( int i = 0; i < it.value().candidate.size(); i++ )
                {
                    candidateArray.append( it.value().candidate[ i ] );
                }
                step.insert( "candidate", candidateArray );
            }

            positionArray.append( step );
        }
        jsonObjectMain[ "position" ] = positionArray;

        // 更新position2部分
        QJsonArray position2Array;
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_position2CfgList.begin(); it != this->_position2CfgList.end(); ++it )
        {
            QJsonObject step;
            step.insert( "id", it.key() );
            step.insert( "title", it.value().title );
            step.insert( "value", it.value().value );
            step.insert( "unit", it.value().unit );
            step.insert( "editable", it.value().editable );

            if ( !it.value().candidate.isEmpty() )
            {
                QJsonArray candidateArray;
                for ( int i = 0; i < it.value().candidate.size(); i++ )
                {
                    candidateArray.append( it.value().candidate[ i ] );
                }
                step.insert( "candidate", candidateArray );
            }

            position2Array.append( step );
        }
        jsonObjectMain[ "position2" ] = position2Array;

        // 更新fengtou部分
        QJsonArray fengtouArray;
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_fengtouCfgList.begin(); it != this->_fengtouCfgList.end(); ++it )
        {
            QJsonObject step;
            step.insert( "id", it.key() );
            step.insert( "title", it.value().title );
            step.insert( "value", it.value().value );
            step.insert( "unit", it.value().unit );
            step.insert( "editable", it.value().editable );

            if ( !it.value().candidate.isEmpty() )
            {
                QJsonArray candidateArray;
                for ( int i = 0; i < it.value().candidate.size(); i++ )
                {
                    candidateArray.append( it.value().candidate[ i ] );
                }
                step.insert( "candidate", candidateArray );
            }

            fengtouArray.append( step );
        }
        jsonObjectMain[ "fengtou" ] = fengtouArray;

        // 更新socketMotor部分
        QJsonArray socketMotorArray;
        for ( QMap< int, StepperMotorConfigItem >::iterator it = this->_socketMotorCfgList.begin(); it != this->_socketMotorCfgList.end(); ++it )
        {
            QJsonObject motorObj;
            motorObj.insert( "id", it.key() );
            motorObj.insert( "title", it.value().title );

            QJsonArray dataArray;
            for ( int i = 0; i < it.value().data.size(); i++ )
            {
                QJsonObject dataObj;
                if ( it.value().data[ i ].unit == "/" )
                {
                    dataObj.insert( "value", "null" );
                }
                else
                {
                    dataObj.insert( "value", it.value().data[ i ].value );
                }
                dataObj.insert( "unit", it.value().data[ i ].unit );
                dataObj.insert( "editable", it.value().data[ i ].editable );

                if ( !it.value().data[ i ].candidate.isEmpty() )
                {
                    QJsonArray candidateArray;
                    for ( int j = 0; j < it.value().data[ i ].candidate.size(); j++ )
                    {
                        candidateArray.append( it.value().data[ i ].candidate[ j ] );
                    }
                    dataObj.insert( "candidate", candidateArray );
                }

                dataArray.append( dataObj );
            }

            motorObj.insert( "data", dataArray );
            socketMotorArray.append( motorObj );
        }
        jsonObjectMain[ "socketMotor" ] = socketMotorArray;

        // 更新fengtouMotor部分
        QJsonArray fengtouMotorArray;
        for ( QMap< int, StepperMotorConfigItem >::iterator it = this->_fengtouMotorCfgList.begin(); it != this->_fengtouMotorCfgList.end(); ++it )
        {
            QJsonObject motorObj;
            motorObj.insert( "id", it.key() );
            motorObj.insert( "title", it.value().title );

            QJsonArray dataArray;
            for ( int i = 0; i < it.value().data.size(); i++ )
            {
                QJsonObject dataObj;
                if ( it.value().data[ i ].unit == "/" )
                {
                    dataObj.insert( "value", "null" );
                }
                else
                {
                    dataObj.insert( "value", it.value().data[ i ].value );
                }
                dataObj.insert( "unit", it.value().data[ i ].unit );
                dataObj.insert( "editable", it.value().data[ i ].editable );

                if ( !it.value().data[ i ].candidate.isEmpty() )
                {
                    QJsonArray candidateArray;
                    for ( int j = 0; j < it.value().data[ i ].candidate.size(); j++ )
                    {
                        candidateArray.append( it.value().data[ i ].candidate[ j ] );
                    }
                    dataObj.insert( "candidate", candidateArray );
                }

                dataArray.append( dataObj );
            }

            motorObj.insert( "data", dataArray );
            fengtouMotorArray.append( motorObj );
        }
        jsonObjectMain[ "fengtouMotor" ] = fengtouMotorArray;

        // 更新otherMotor部分
        QJsonArray otherMotorArray;
        for ( QMap< int, StepperMotorConfigItem >::iterator it = this->_otherMotorCfgList.begin(); it != this->_otherMotorCfgList.end(); ++it )
        {
            QJsonObject motorObj;
            motorObj.insert( "id", it.key() );
            motorObj.insert( "title", it.value().title );

            QJsonArray dataArray;
            for ( int i = 0; i < it.value().data.size(); i++ )
            {
                QJsonObject dataObj;
                if ( it.value().data[ i ].unit == "/" )
                {
                    dataObj.insert( "value", "null" );
                }
                else
                {
                    dataObj.insert( "value", it.value().data[ i ].value );
                }
                dataObj.insert( "unit", it.value().data[ i ].unit );
                dataObj.insert( "editable", it.value().data[ i ].editable );

                if ( !it.value().data[ i ].candidate.isEmpty() )
                {
                    QJsonArray candidateArray;
                    for ( int j = 0; j < it.value().data[ i ].candidate.size(); j++ )
                    {
                        candidateArray.append( it.value().data[ i ].candidate[ j ] );
                    }
                    dataObj.insert( "candidate", candidateArray );
                }

                dataArray.append( dataObj );
            }

            motorObj.insert( "data", dataArray );
            otherMotorArray.append( motorObj );
        }
        jsonObjectMain[ "otherMotor" ] = otherMotorArray;

        // 重新打开文件进行写入
        if ( !file.open( QIODevice::WriteOnly ) )
        {
            qDebug() << "无法打开文件进行写入:" << fileAddr;
            return -1;
        }

        QJsonDocument newJsonDocument( jsonObjectMain );
        file.write( newJsonDocument.toJson() );
        qDebug() << "机器相关配置保存成功";
        file.close();
        return 1;
    }
    catch ( const std::exception& e )
    {
        qDebug() << "保存机器相关配置时发生错误:" << e.what();
        file.close();
        return -1;
    }
}

// 只保存用户配置到文件
quint8 ReadMachineFileConfig::saveUserConfig( QString fileAddr )
{
    QFile file( fileAddr );

    // 首先读取现有文件内容
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        qDebug() << "无法打开文件进行读取:" << fileAddr;
        return -1;
    }

    // 读取文件内容到QByteArray
    QByteArray jsonBytes = file.readAll();
    file.close();

    // 将QByteArray解析为QJsonDocument
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    // 确保Json文档有效
    if ( jsonDocument.isNull() )
    {
        qDebug() << "JSON文档无效!";
        return -1;
    }

    // 将QJsonDocument转换为QJsonObject
    QJsonObject jsonObjectMain = jsonDocument.object();

    try
    {
        // 更新otherCfg部分
        QJsonArray otherArray;
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_otherCfgList.begin(); it != this->_otherCfgList.end(); ++it )
        {
            QJsonObject step;
            step.insert( "id", it.key() );
            step.insert( "title", it.value().title );
            step.insert( "value", it.value().value );
            step.insert( "unit", it.value().unit );
            step.insert( "editable", it.value().editable );

            if ( !it.value().candidate.isEmpty() )
            {
                QJsonArray candidateArray;
                for ( int i = 0; i < it.value().candidate.size(); i++ )
                {
                    candidateArray.append( it.value().candidate[ i ] );
                }
                step.insert( "candidate", candidateArray );
            }

            otherArray.append( step );
        }
        jsonObjectMain[ "other" ] = otherArray;

        // 更新userCfg部分
        QJsonArray userArray;
        for ( QMap< int, MachineFlieConfigItem >::iterator it = this->_userCfgList.begin(); it != this->_userCfgList.end(); ++it )
        {
            QJsonObject step;
            step.insert( "id", it.key() );
            step.insert( "title", it.value().title );
            step.insert( "value", it.value().value );
            step.insert( "unit", it.value().unit );
            step.insert( "editable", it.value().editable );

            if ( !it.value().candidate.isEmpty() )
            {
                QJsonArray candidateArray;
                for ( int i = 0; i < it.value().candidate.size(); i++ )
                {
                    candidateArray.append( it.value().candidate[ i ] );
                }
                step.insert( "candidate", candidateArray );
            }

            userArray.append( step );
        }
        jsonObjectMain[ "user" ] = userArray;

        // 重新打开文件进行写入
        if ( !file.open( QIODevice::WriteOnly ) )
        {
            qDebug() << "无法打开文件进行写入:" << fileAddr;
            return -1;
        }

        QJsonDocument newJsonDocument( jsonObjectMain );
        file.write( newJsonDocument.toJson() );
        qDebug() << "用户配置保存成功";
        file.close();
        return 1;
    }
    catch ( const std::exception& e )
    {
        qDebug() << "保存用户配置时发生错误:" << e.what();
        file.close();
        return -1;
    }
}

// 只保存电眼配置到文件
quint8 ReadMachineFileConfig::saveEEyeConfig( QString fileAddr )
{
    QFile file( fileAddr );

    // 首先读取现有文件内容
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        qDebug() << "无法打开文件进行读取:" << fileAddr;
        return -1;
    }

    // 读取文件内容到QByteArray
    QByteArray jsonBytes = file.readAll();
    file.close();

    // 将QByteArray解析为QJsonDocument
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    // 确保Json文档有效
    if ( jsonDocument.isNull() )
    {
        qDebug() << "JSON文档无效!";
        return -1;
    }

    // 将QJsonDocument转换为QJsonObject
    QJsonObject jsonObjectMain = jsonDocument.object();

    try
    {
        // 只更新EEye部分
        QJsonObject eeyeObj;
        eeyeObj[ "realCount" ] = _eeyeCfg.realCount;

        QJsonArray itemsArray;
        for ( int i = 0; i < _eeyeCfg.items.size(); ++i )
        {
            QJsonObject itemObj;
            itemObj[ "id" ]              = _eeyeCfg.items[ i ].id;
            itemObj[ "sensitivity" ]     = _eeyeCfg.items[ i ].sensitivity;
            itemObj[ "inductionDelay" ]  = _eeyeCfg.items[ i ].inductionDelay;
            itemObj[ "stFreq" ]          = _eeyeCfg.items[ i ].stFreq;
            itemObj[ "endFreq" ]         = _eeyeCfg.items[ i ].endFreq;
            itemObj[ "mappedId" ]        = _eeyeCfg.items[ i ].mappedId;
            itemObj[ "disConThreshold" ] = _eeyeCfg.items[ i ].disConThreshold;
            itemObj[ "wrapThreshold" ]   = _eeyeCfg.items[ i ].wrapThreshold;

            itemsArray.append( itemObj );
        }

        eeyeObj[ "items" ]       = itemsArray;
        jsonObjectMain[ "EEye" ] = eeyeObj;

        // 重新打开文件进行写入
        if ( !file.open( QIODevice::WriteOnly ) )
        {
            qDebug() << "无法打开文件进行写入:" << fileAddr;
            return -1;
        }

        QJsonDocument newJsonDocument( jsonObjectMain );
        file.write( newJsonDocument.toJson() );
        qDebug() << "电眼配置保存成功";
        file.close();
        return 1;
    }
    catch ( const std::exception& e )
    {
        qDebug() << "保存电眼配置时发生错误:" << e.what();
        file.close();
        return -1;
    }
}

// 只保存步进电机配置到文件
quint8 ReadMachineFileConfig::saveStepMotorConfig( QString fileAddr )
{
    QFile file( fileAddr );

    // 首先读取现有文件内容
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        qDebug() << "无法打开文件进行读取:" << fileAddr;
        return -1;
    }

    // 读取文件内容到QByteArray
    QByteArray jsonBytes = file.readAll();
    file.close();

    // 将QByteArray解析为QJsonDocument
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    // 确保Json文档有效
    if ( jsonDocument.isNull() )
    {
        qDebug() << "JSON文档无效!";
        return -1;
    }

    // 将QJsonDocument转换为QJsonObject
    QJsonObject jsonObjectMain = jsonDocument.object();

    try
    {
        // 更新socketMotor部分
        QJsonArray socketMotorArray;
        for ( QMap< int, StepperMotorConfigItem >::iterator it = this->_socketMotorCfgList.begin(); it != this->_socketMotorCfgList.end(); ++it )
        {
            QJsonObject motorObj;
            motorObj.insert( "id", it.key() );
            motorObj.insert( "title", it.value().title );

            QJsonArray dataArray;
            for ( int i = 0; i < it.value().data.size(); i++ )
            {
                QJsonObject dataObj;
                dataObj.insert( "value", it.value().data[ i ].value );
                dataObj.insert( "unit", it.value().data[ i ].unit );
                dataObj.insert( "editable", it.value().data[ i ].editable );

                if ( !it.value().data[ i ].candidate.isEmpty() )
                {
                    QJsonArray candidateArray;
                    for ( int j = 0; j < it.value().data[ i ].candidate.size(); j++ )
                    {
                        candidateArray.append( it.value().data[ i ].candidate[ j ] );
                    }
                    dataObj.insert( "candidate", candidateArray );
                }

                dataArray.append( dataObj );
            }

            motorObj.insert( "data", dataArray );
            socketMotorArray.append( motorObj );
        }
        jsonObjectMain.insert( "socketMotor", socketMotorArray );

        // 更新fengtouMotor部分
        QJsonArray fengtouMotorArray;
        for ( QMap< int, StepperMotorConfigItem >::iterator it = this->_fengtouMotorCfgList.begin(); it != this->_fengtouMotorCfgList.end(); ++it )
        {
            QJsonObject motorObj;
            motorObj.insert( "id", it.key() );
            motorObj.insert( "title", it.value().title );

            QJsonArray dataArray;
            for ( int i = 0; i < it.value().data.size(); i++ )
            {
                QJsonObject dataObj;
                dataObj.insert( "value", it.value().data[ i ].value );
                dataObj.insert( "unit", it.value().data[ i ].unit );
                dataObj.insert( "editable", it.value().data[ i ].editable );

                if ( !it.value().data[ i ].candidate.isEmpty() )
                {
                    QJsonArray candidateArray;
                    for ( int j = 0; j < it.value().data[ i ].candidate.size(); j++ )
                    {
                        candidateArray.append( it.value().data[ i ].candidate[ j ] );
                    }
                    dataObj.insert( "candidate", candidateArray );
                }

                dataArray.append( dataObj );
            }

            motorObj.insert( "data", dataArray );
            fengtouMotorArray.append( motorObj );
        }
        jsonObjectMain.insert( "fengtouMotor", fengtouMotorArray );

        // 更新otherMotor部分
        QJsonArray otherMotorArray;
        for ( QMap< int, StepperMotorConfigItem >::iterator it = this->_otherMotorCfgList.begin(); it != this->_otherMotorCfgList.end(); ++it )
        {
            QJsonObject motorObj;
            motorObj.insert( "id", it.key() );
            motorObj.insert( "title", it.value().title );

            QJsonArray dataArray;
            for ( int i = 0; i < it.value().data.size(); i++ )
            {
                QJsonObject dataObj;
                dataObj.insert( "value", it.value().data[ i ].value );
                dataObj.insert( "unit", it.value().data[ i ].unit );
                dataObj.insert( "editable", it.value().data[ i ].editable );

                if ( !it.value().data[ i ].candidate.isEmpty() )
                {
                    QJsonArray candidateArray;
                    for ( int j = 0; j < it.value().data[ i ].candidate.size(); j++ )
                    {
                        candidateArray.append( it.value().data[ i ].candidate[ j ] );
                    }
                    dataObj.insert( "candidate", candidateArray );
                }

                dataArray.append( dataObj );
            }

            motorObj.insert( "data", dataArray );
            otherMotorArray.append( motorObj );
        }
        jsonObjectMain.insert( "otherMotor", otherMotorArray );

        // 重新打开文件进行写入
        if ( !file.open( QIODevice::WriteOnly ) )
        {
            qDebug() << "无法打开文件进行写入:" << fileAddr;
            return -1;
        }

        QJsonDocument newJsonDocument( jsonObjectMain );
        file.write( newJsonDocument.toJson() );
        qDebug() << "步进电机配置保存成功";
        file.close();
        return 1;
    }
    catch ( const std::exception& e )
    {
        qDebug() << "保存步进电机配置时发生错误:" << e.what();
        file.close();
        return -1;
    }
}

// 只保存伺服电机配置到文件
quint8 ReadMachineFileConfig::saveServoConfig( QString fileAddr )
{
    QFile file( fileAddr );

    // 首先读取现有文件内容
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        qDebug() << "无法打开文件进行读取:" << fileAddr;
        return -1;
    }

    // 读取文件内容到QByteArray
    QByteArray jsonBytes = file.readAll();
    file.close();

    // 将QByteArray解析为QJsonDocument
    QJsonDocument jsonDocument = QJsonDocument::fromJson( jsonBytes );

    // 确保Json文档有效
    if ( jsonDocument.isNull() )
    {
        qDebug() << "JSON文档无效!";
        return -1;
    }

    // 将QJsonDocument转换为QJsonObject
    QJsonObject jsonObjectMain = jsonDocument.object();

    try
    {
        // 更新Servo部分
        QJsonArray servoArray;
        // 遍历servoCfg数组
        for ( QMap< int, ServoCfgItem >::iterator it = this->_servoList.begin(); it != this->_servoList.end(); ++it )
        {
            // 获取数组元素
            ServoCfgItem& item = it.value();
            QJsonObject   servo;
            servo.insert( "id", item.id );
            servo.insert( "title", item.title );
            servo.insert( "value", item.value );
            servo.insert( "unit", item.unit );
            servo.insert( "editable", item.editable );
            servoArray.append( servo );
        }
        jsonObjectMain.insert( "Servo", servoArray );

        // 重新打开文件进行写入
        if ( !file.open( QIODevice::WriteOnly ) )
        {
            qDebug() << "无法打开文件进行写入:" << fileAddr;
            return -1;
        }

        QJsonDocument newJsonDocument( jsonObjectMain );
        file.write( newJsonDocument.toJson() );
        qDebug() << "伺服电机配置保存成功";
        file.close();
        return 1;
    }
    catch ( const std::exception& e )
    {
        qDebug() << "保存伺服电机配置时发生错误:" << e.what();
        file.close();
        return -1;
    }
}

MachineParams ReadMachineFileConfig::makeMachineParamFrame()
{
    MachineParams params;
    memset( &params, 0, sizeof( params ) );
    // Populate params from _basicCfgList
    if ( _basicCfgList.contains( 1 ) )
        params.machine_type = static_cast< quint8 >( _basicCfgList[ 1 ].value );
    if ( _basicCfgList.contains( 2 ) )
        params.max_niddle = static_cast< quint16 >( _basicCfgList[ 2 ].value );
    if ( _basicCfgList.contains( 3 ) )
        params.motor_zero = static_cast< quint16 >( _basicCfgList[ 3 ].value );
    if ( _basicCfgList.contains( 4 ) )
        params.feng_auto = static_cast< quint8 >( _basicCfgList[ 4 ].value );
    if ( _basicCfgList.contains( 5 ) )
        params.g_left_pos = static_cast< quint16 >( _basicCfgList[ 5 ].value );
    if ( _basicCfgList.contains( 6 ) )
        params.g_righ_pos = static_cast< quint16 >( _basicCfgList[ 6 ].value );
    if ( _basicCfgList.contains( 7 ) )
        params.wg_fast_en = static_cast< quint8 >( _basicCfgList[ 7 ].value );
    if ( _basicCfgList.contains( 8 ) )
        params.wg_dec_en = static_cast< quint8 >( _basicCfgList[ 8 ].value );
    if ( _basicCfgList.contains( 9 ) )
        params.wg_dec_auto = static_cast< quint8 >( _basicCfgList[ 9 ].value );
    if ( _basicCfgList.contains( 10 ) )
        params.no1_wg_pos = static_cast< quint16 >( _basicCfgList[ 10 ].value );
    if ( _basicCfgList.contains( 11 ) )
        params.no2_righ_pos = static_cast< quint16 >( _basicCfgList[ 11 ].value );
    if ( _basicCfgList.contains( 12 ) )
        params.eye_enable = static_cast< quint8 >( _basicCfgList[ 12 ].value );
    if ( _basicCfgList.contains( 13 ) )
        params.ktf_start_num = static_cast< quint8 >( _basicCfgList[ 13 ].value );
    if ( _basicCfgList.contains( 14 ) )
        params.ktf_step_num = static_cast< quint8 >( _basicCfgList[ 14 ].value );
    if ( _basicCfgList.contains( 15 ) )
        params.power_xuzhi = static_cast< quint8 >( _basicCfgList[ 15 ].value );

    // Populate params from _needleCfgList
    if ( _needleCfgList.contains( 1 ) )
        params.xuan_jxing = static_cast< quint8 >( _needleCfgList[ 1 ].value );
    if ( _needleCfgList.contains( 2 ) )
        params.xuan_pai = static_cast< quint8 >( _needleCfgList[ 2 ].value );
    if ( _needleCfgList.contains( 3 ) )
        params.duan_num = static_cast< quint8 >( _needleCfgList[ 3 ].value );
    if ( _needleCfgList.contains( 4 ) )
        params.no1_hua_start = static_cast< quint8 >( _needleCfgList[ 4 ].value );
    if ( _needleCfgList.contains( 5 ) )
        params.no1_hua_end = static_cast< quint8 >( _needleCfgList[ 5 ].value );
    if ( _needleCfgList.contains( 6 ) )
        params.no1_hua_num = static_cast< quint8 >( _needleCfgList[ 6 ].value );
    if ( _needleCfgList.contains( 7 ) )
        params.no2_hua_start = static_cast< quint8 >( _needleCfgList[ 7 ].value );
    if ( _needleCfgList.contains( 8 ) )
        params.no2_hua_end = static_cast< quint8 >( _needleCfgList[ 8 ].value );
    if ( _needleCfgList.contains( 9 ) )
        params.no2_hua_num = static_cast< quint8 >( _needleCfgList[ 9 ].value );
    if ( _needleCfgList.contains( 10 ) )
        params.hua_zu_num = static_cast< quint8 >( _needleCfgList[ 10 ].value );
    if ( _needleCfgList.contains( 11 ) )
        params.xuan_ahead = static_cast< quint8 >( _needleCfgList[ 11 ].value );
    if ( _needleCfgList.contains( 12 ) )
        params.lmao_jxing = static_cast< quint8 >( _needleCfgList[ 12 ].value );
    if ( _needleCfgList.contains( 13 ) )
        params.lmao_pai = static_cast< quint8 >( _needleCfgList[ 13 ].value );
    if ( _needleCfgList.contains( 14 ) )
        params.lmao_num = static_cast< quint8 >( _needleCfgList[ 14 ].value );
    if ( _needleCfgList.contains( 15 ) )
        params.xj_x_pos = static_cast< quint16 >( _needleCfgList[ 15 ].value );
    if ( _needleCfgList.contains( 16 ) )
        params.jq_x_pos = static_cast< quint16 >( _needleCfgList[ 16 ].value );
    if ( _needleCfgList.contains( 17 ) )
        params.no1_x_pos = static_cast< quint16 >( _needleCfgList[ 17 ].value );
    if ( _needleCfgList.contains( 18 ) )
        params.no2_x_pos = static_cast< quint16 >( _needleCfgList[ 18 ].value );
    if ( _needleCfgList.contains( 19 ) )
        params.no3_x_pos = static_cast< quint16 >( _needleCfgList[ 19 ].value );
    if ( _needleCfgList.contains( 20 ) )
        params.no4_x_pos = static_cast< quint16 >( _needleCfgList[ 20 ].value );
    if ( _needleCfgList.contains( 21 ) )
        params.no5_x_pos = static_cast< quint16 >( _needleCfgList[ 21 ].value );
    if ( _needleCfgList.contains( 22 ) )
        params.no6_x_pos = static_cast< quint16 >( _needleCfgList[ 22 ].value );
    if ( _needleCfgList.contains( 23 ) )
        params.f1_x_pos = static_cast< quint16 >( _needleCfgList[ 23 ].value );
    if ( _needleCfgList.contains( 24 ) )
        params.lmao_for_pos = static_cast< quint16 >( _needleCfgList[ 24 ].value );
    if ( _needleCfgList.contains( 25 ) )
        params.lmao_neg_pos = static_cast< quint16 >( _needleCfgList[ 25 ].value );
    if ( _needleCfgList.contains( 26 ) )
        params.xj_x_ahead = static_cast< quint8 >( _needleCfgList[ 26 ].value );
    if ( _needleCfgList.contains( 27 ) )
        params.jq_x_ahead = static_cast< quint8 >( _needleCfgList[ 27 ].value );
    if ( _needleCfgList.contains( 28 ) )
        params.no1_x_ahead = static_cast< quint8 >( _needleCfgList[ 28 ].value );
    if ( _needleCfgList.contains( 29 ) )
        params.no2_x_ahead = static_cast< quint8 >( _needleCfgList[ 29 ].value );
    if ( _needleCfgList.contains( 30 ) )
        params.no3_x_ahead = static_cast< quint8 >( _needleCfgList[ 30 ].value );
    if ( _needleCfgList.contains( 31 ) )
        params.no4_x_ahead = static_cast< quint8 >( _needleCfgList[ 31 ].value );
    if ( _needleCfgList.contains( 32 ) )
        params.no5_x_ahead = static_cast< quint8 >( _needleCfgList[ 32 ].value );
    if ( _needleCfgList.contains( 33 ) )
        params.no6_x_ahead = static_cast< quint8 >( _needleCfgList[ 33 ].value );
    if ( _needleCfgList.contains( 34 ) )
        params.f1_x_ahead = static_cast< quint8 >( _needleCfgList[ 34 ].value );

    // Populate params from _peripheralCfgList
    if ( _peripheralCfgList.contains( 1 ) )
        params.m_sz_pos = static_cast< quint16 >( _peripheralCfgList[ 1 ].value );
    if ( _peripheralCfgList.contains( 2 ) )
        params.m_sz_ahead = static_cast< quint16 >( _peripheralCfgList[ 2 ].value );
    if ( _peripheralCfgList.contains( 3 ) )
        params.m_sz_jian = static_cast< quint16 >( _peripheralCfgList[ 3 ].value );
    if ( _peripheralCfgList.contains( 4 ) )
        params.m_sz_half_delay = static_cast< quint8 >( _peripheralCfgList[ 4 ].value );
    if ( _peripheralCfgList.contains( 5 ) )
        params.m_sz_xj1 = static_cast< quint8 >( _peripheralCfgList[ 5 ].value );
    if ( _peripheralCfgList.contains( 6 ) )
        params.m_sz_xj_qh = static_cast< quint8 >( _peripheralCfgList[ 6 ].value );
    if ( _peripheralCfgList.contains( 7 ) )
        params.m_sz_jq1 = static_cast< quint8 >( _peripheralCfgList[ 7 ].value );
    if ( _peripheralCfgList.contains( 8 ) )
        params.m_sz_jq2 = static_cast< quint8 >( _peripheralCfgList[ 8 ].value );
    if ( _peripheralCfgList.contains( 9 ) )
        params.nozzle_ahead = static_cast< quint16 >( _peripheralCfgList[ 9 ].value );
    if ( _peripheralCfgList.contains( 10 ) )
        params.tian_jiange = static_cast< quint8 >( _peripheralCfgList[ 10 ].value );
    if ( _peripheralCfgList.contains( 11 ) )
        params.xj_sz_pos = static_cast< quint16 >( _peripheralCfgList[ 11 ].value );
    if ( _peripheralCfgList.contains( 12 ) )
        params.jq_sz_pos = static_cast< quint16 >( _peripheralCfgList[ 12 ].value );
    if ( _peripheralCfgList.contains( 13 ) )
        params.no1_sz_pos = static_cast< quint16 >( _peripheralCfgList[ 13 ].value );
    if ( _peripheralCfgList.contains( 14 ) )
        params.no2_sz_pos = static_cast< quint16 >( _peripheralCfgList[ 14 ].value );
    if ( _peripheralCfgList.contains( 15 ) )
        params.no3_sz_pos = static_cast< quint16 >( _peripheralCfgList[ 15 ].value );
    if ( _peripheralCfgList.contains( 16 ) )
        params.no4_sz_pos = static_cast< quint16 >( _peripheralCfgList[ 16 ].value );
    if ( _peripheralCfgList.contains( 17 ) )
        params.no5_sz_pos = static_cast< quint16 >( _peripheralCfgList[ 17 ].value );
    if ( _peripheralCfgList.contains( 18 ) )
        params.no6_sz_pos = static_cast< quint16 >( _peripheralCfgList[ 18 ].value );
    if ( _peripheralCfgList.contains( 19 ) )
        params.triangle_ahead = static_cast< quint8 >( _peripheralCfgList[ 19 ].value );
    if ( _peripheralCfgList.contains( 20 ) )
        params.out_wg_time = static_cast< quint16 >( _peripheralCfgList[ 20 ].value );

    // 三角相关参数
    // position部分
    if ( _positionCfgList.contains( 1 ) )
        params.sanjiao_par.par.left_hlt_pos = static_cast< quint16 >( _positionCfgList[ 1 ].value );  // 左活络头位置
    if ( _positionCfgList.contains( 2 ) )
        params.sanjiao_par.par.righ_hlt_pos = static_cast< quint16 >( _positionCfgList[ 2 ].value );  // 右活络头位置
    if ( _positionCfgList.contains( 3 ) )
        params.sanjiao_par.par.q_left_pos = static_cast< quint16 >( _positionCfgList[ 3 ].value );  // 左揿针位置
    if ( _positionCfgList.contains( 4 ) )
        params.sanjiao_par.par.q_righ_pos = static_cast< quint16 >( _positionCfgList[ 4 ].value );  // 右揿针位置
    if ( _positionCfgList.contains( 5 ) )
        params.sanjiao_par.par.left_pick_pos = static_cast< quint16 >( _positionCfgList[ 5 ].value );  // 左挑位置(140)
    if ( _positionCfgList.contains( 6 ) )
        params.sanjiao_par.par.right_pick_pos = static_cast< quint16 >( _positionCfgList[ 6 ].value );  // 右挑位置(140)
    if ( _positionCfgList.contains( 7 ) )
        params.sanjiao_par.par.m_for_pos = static_cast< quint16 >( _positionCfgList[ 7 ].value );  // 正向毛刀位置
    if ( _positionCfgList.contains( 8 ) )
        params.sanjiao_par.par.m_rev_pos = static_cast< quint16 >( _positionCfgList[ 8 ].value );  // 反向毛刀位置
    if ( _positionCfgList.contains( 9 ) )
        params.sanjiao_par.par.triangle1_pos = static_cast< quint16 >( _positionCfgList[ 9 ].value );  // 1口打松三角位置(0度)
    if ( _positionCfgList.contains( 10 ) )
        params.sanjiao_par.par.triangle2_pos = static_cast< quint16 >( _positionCfgList[ 10 ].value );  // 2口打松三角位置(30度)
    if ( _positionCfgList.contains( 11 ) )
        params.sanjiao_par.par.half_in_half_pos = static_cast< quint16 >( _positionCfgList[ 11 ].value );  // 哈夫半位进半位置(280)
    if ( _positionCfgList.contains( 12 ) )
        params.sanjiao_par.par.half_in_full_pos = static_cast< quint16 >( _positionCfgList[ 12 ].value );  // 哈夫半位进全位置(130)
    if ( _positionCfgList.contains( 13 ) )
        params.sanjiao_par.par.half_out_half_pos = static_cast< quint16 >( _positionCfgList[ 13 ].value );  // 哈夫半位出半位置(230)
    if ( _positionCfgList.contains( 14 ) )
        params.sanjiao_par.par.half_out_full_pos = static_cast< quint16 >( _positionCfgList[ 14 ].value );  // 哈夫半位出全位置(90)
    if ( _positionCfgList.contains( 15 ) )
        params.sanjiao_par.par.half_in_flower_pos = static_cast< quint16 >( _positionCfgList[ 15 ].value );  // 哈夫半位进花位置(140)
    if ( _positionCfgList.contains( 16 ) )
        params.sanjiao_par.par.t1_check_pos = static_cast< quint16 >( _positionCfgList[ 16 ].value );  // T1检查位置(180)
    if ( _positionCfgList.contains( 17 ) )
        params.sanjiao_par.par.t2_circle_pos = static_cast< quint16 >( _positionCfgList[ 17 ].value );  // T2圆位置(200)
    if ( _positionCfgList.contains( 18 ) )
        params.sanjiao_par.par.left_act_half_pos = static_cast< quint16 >( _positionCfgList[ 18 ].value );  // 左活络头半位(70)
    if ( _positionCfgList.contains( 19 ) )
        params.sanjiao_par.par.left_act_full_pos = static_cast< quint16 >( _positionCfgList[ 19 ].value );  // 左活络头全位(250)
    if ( _positionCfgList.contains( 20 ) )
        params.sanjiao_par.par.right_act_half_pos = static_cast< quint16 >( _positionCfgList[ 20 ].value );  // 右活络头半位(70)
    if ( _positionCfgList.contains( 21 ) )
        params.sanjiao_par.par.right_act_full_pos = static_cast< quint16 >( _positionCfgList[ 21 ].value );  // 右活络头全位(250)
    if ( _positionCfgList.contains( 22 ) )
        params.sanjiao_par.par.back_half_pos = static_cast< quint16 >( _positionCfgList[ 22 ].value );  // 退圈刀半位置(230)
    if ( _positionCfgList.contains( 23 ) )
        params.sanjiao_par.par.back_full_pos = static_cast< quint16 >( _positionCfgList[ 23 ].value );  // 退圈刀全位置(50)
    if ( _positionCfgList.contains( 24 ) )
        params.sanjiao_par.par.left_angle_half_pos = static_cast< quint16 >( _positionCfgList[ 24 ].value );  // 左棱角半位置(270)

    // position2部分
    if ( _position2CfgList.contains( 1 ) )
        params.sanjiao_par.par.left_angle_full_pos = static_cast< quint16 >( _position2CfgList[ 1 ].value );  // 左棱角全位置(180)
    if ( _position2CfgList.contains( 2 ) )
        params.sanjiao_par.par.right_angle_half_pos = static_cast< quint16 >( _position2CfgList[ 2 ].value );  // 右棱角半位置(100)
    if ( _position2CfgList.contains( 3 ) )
        params.sanjiao_par.par.right_angle_full_pos = static_cast< quint16 >( _position2CfgList[ 3 ].value );  // 右棱角全位置(280)
    if ( _position2CfgList.contains( 4 ) )
        params.sanjiao_par.par.rubber_half_pos = static_cast< quint16 >( _position2CfgList[ 4 ].value );  // 橡筋半位置(210)
    if ( _position2CfgList.contains( 5 ) )
        params.sanjiao_par.par.rubber_full_pos = static_cast< quint16 >( _position2CfgList[ 5 ].value );  // 橡筋全位置(30)
    if ( _position2CfgList.contains( 6 ) )
        params.sanjiao_par.par.pick_blade_pos = static_cast< quint16 >( _position2CfgList[ 6 ].value );  // 挑刀位置(260)
    if ( _position2CfgList.contains( 7 ) )
        params.sanjiao_par.par.press_blade_pos = static_cast< quint16 >( _position2CfgList[ 7 ].value );  // 压刀位置(240)
    if ( _position2CfgList.contains( 8 ) )
        params.sanjiao_par.par.right_press_pos = static_cast< quint16 >( _position2CfgList[ 8 ].value );  // 右压选针器刀位置(240)
    if ( _position2CfgList.contains( 9 ) )
        params.sanjiao_par.par.left_press_pos = static_cast< quint16 >( _position2CfgList[ 9 ].value );  // 左压选针器刀位置(200)
    if ( _position2CfgList.contains( 10 ) )
        params.sanjiao_par.par.foot_blade_pos = static_cast< quint16 >( _position2CfgList[ 10 ].value );  // 脚低加固起针刀位置(170)
    if ( _position2CfgList.contains( 11 ) )
        params.sanjiao_par.par.head_blade_pos = static_cast< quint16 >( _position2CfgList[ 11 ].value );  // 缝头转移起针刀位置(200)
    if ( _position2CfgList.contains( 12 ) )
        params.sanjiao_par.par.boat_tri1_pos = static_cast< quint16 >( _position2CfgList[ 12 ].value );  // 船三角1位置(180)
    if ( _position2CfgList.contains( 13 ) )
        params.sanjiao_par.par.boat_tri2_pos = static_cast< quint16 >( _position2CfgList[ 13 ].value );  // 船三角2位置(180)
    if ( _position2CfgList.contains( 14 ) )
        params.sanjiao_par.par.boat_tri3_pos = static_cast< quint16 >( _position2CfgList[ 14 ].value );  // 船三角3位置(180)

    // 缝头参数
    if ( _fengtouCfgList.contains( 1 ) )
        params.sewing_par.calibrationMode = static_cast< quint8 >( _fengtouCfgList[ 1 ].value );
    if ( _fengtouCfgList.contains( 2 ) )
        params.sewing_par.stopDeceleration = static_cast< quint16 >( _fengtouCfgList[ 2 ].value );
    if ( _fengtouCfgList.contains( 3 ) )
        params.sewing_par.motorGearNum = static_cast< quint16 >( _fengtouCfgList[ 3 ].value );
    if ( _fengtouCfgList.contains( 4 ) )
        params.sewing_par.needleGearNum = static_cast< quint16 >( _fengtouCfgList[ 4 ].value );
    if ( _fengtouCfgList.contains( 5 ) )
        params.sewing_par.stitchWidth = static_cast< quint16 >( _fengtouCfgList[ 5 ].value );
    if ( _fengtouCfgList.contains( 6 ) )
        params.sewing_par.firstNeedlePosition = static_cast< quint16 >( _fengtouCfgList[ 6 ].value );
    if ( _fengtouCfgList.contains( 7 ) )
        params.sewing_par.swingArmPos1 = static_cast< quint16 >( _fengtouCfgList[ 7 ].value );
    if ( _fengtouCfgList.contains( 8 ) )
        params.sewing_par.swingArmPos2 = static_cast< quint16 >( _fengtouCfgList[ 8 ].value );
    if ( _fengtouCfgList.contains( 9 ) )
        params.sewing_par.swingArmPos3 = static_cast< quint16 >( _fengtouCfgList[ 9 ].value );
    if ( _fengtouCfgList.contains( 10 ) )
        params.sewing_par.swingArmPos4 = static_cast< quint16 >( _fengtouCfgList[ 10 ].value );
    if ( _fengtouCfgList.contains( 11 ) )
        params.sewing_par.swingArmPos5 = static_cast< quint16 >( _fengtouCfgList[ 11 ].value );
    if ( _fengtouCfgList.contains( 12 ) )
        params.sewing_par.swingArmPos6 = static_cast< quint16 >( _fengtouCfgList[ 12 ].value );
    if ( _fengtouCfgList.contains( 13 ) )
        params.sewing_par.gripperPos7 = static_cast< quint16 >( _fengtouCfgList[ 13 ].value );
    if ( _fengtouCfgList.contains( 14 ) )
        params.sewing_par.gripperPos8 = static_cast< quint16 >( _fengtouCfgList[ 14 ].value );
    if ( _fengtouCfgList.contains( 15 ) )
        params.sewing_par.gripperPos9 = static_cast< quint16 >( _fengtouCfgList[ 15 ].value );
    if ( _fengtouCfgList.contains( 16 ) )
        params.sewing_par.stopCompensation = static_cast< quint16 >( _fengtouCfgList[ 16 ].value );
    if ( _fengtouCfgList.contains( 17 ) )
        params.sewing_par.stopErrorTolerance = static_cast< quint16 >( _fengtouCfgList[ 17 ].value );
    if ( _fengtouCfgList.contains( 18 ) )
        params.sewing_par.swingArmPos10 = static_cast< quint16 >( _fengtouCfgList[ 18 ].value );
    if ( _fengtouCfgList.contains( 19 ) )
        params.sewing_par.needlePinTolerance = static_cast< quint16 >( _fengtouCfgList[ 19 ].value );
    if ( _fengtouCfgList.contains( 20 ) )
        params.sewing_par.quickMode = static_cast< quint8 >( _fengtouCfgList[ 20 ].value );
    if ( _fengtouCfgList.contains( 21 ) )
        params.sewing_par.swingMode = static_cast< quint8 >( _fengtouCfgList[ 21 ].value );

    // 电机参数 - socketMotor
    for ( int i = 1; i <= 9; i++ )
    {
        if ( _socketMotorCfgList.contains( i ) )
        {
            params.motor_par[ i - 1 ].runCurrent     = static_cast< quint16 >( _socketMotorCfgList[ i ].data[ 0 ].value * 10 );
            params.motor_par[ i - 1 ].fixedCurrent   = static_cast< quint16 >( _socketMotorCfgList[ i ].data[ 1 ].value * 10 );
            params.motor_par[ i - 1 ].runFrequency   = static_cast< quint16 >( _socketMotorCfgList[ i ].data[ 2 ].value * 1000 );
            if( _socketMotorCfgList[ i ].data[ 3 ].unit != "/") {
                params.motor_par[ i - 1 ].resetFrequency = static_cast< quint16 >( _socketMotorCfgList[ i ].data[ 3 ].value * 1000 );
            } else {
                params.motor_par[ i - 1 ].resetFrequency = 0xFFFF;
            }
            if( _socketMotorCfgList[ i ].data[ 4 ].unit != "/") {
                params.motor_par[ i - 1 ].runRange       = static_cast< quint16 >( _socketMotorCfgList[ i ].data[ 4 ].value );
            } else {
                params.motor_par[ i - 1 ].runRange       = 0xFFFF;
            }
            params.motor_par[ i - 1 ].direction      = static_cast< quint8 >( _socketMotorCfgList[ i ].data[ 5 ].value );
            if( _socketMotorCfgList[ i ].data[ 6 ].unit != "/") {
                params.motor_par[ i - 1 ].zeroPosition   = static_cast< quint8 >( _socketMotorCfgList[ i ].data[ 6 ].value );
            } else {
                params.motor_par[ i - 1 ].zeroPosition   = 0xFF;
            }
            if( _socketMotorCfgList[ i ].data[ 7 ].unit != "/") {
                params.motor_par[ i - 1 ].detectEnable   = static_cast< quint8 >( _socketMotorCfgList[ i ].data[ 7 ].value );
            } else {
                params.motor_par[ i - 1 ].detectEnable   = 0xFF;
            }
            params.motor_par[ i - 1 ].enable         = static_cast< quint8 >( _socketMotorCfgList[ i ].data[ 8 ].value );
        }
    }

    // 电机参数 - fengtouMotor
    for ( int i = 1; i <= 8; i++ )
    {
        if ( _fengtouMotorCfgList.contains( i ) )
        {
            params.motor_par[ i + 8 ].runCurrent     = static_cast< quint16 >( _fengtouMotorCfgList[ i ].data[ 0 ].value * 10 );
            params.motor_par[ i + 8 ].fixedCurrent   = static_cast< quint16 >( _fengtouMotorCfgList[ i ].data[ 1 ].value * 10 );
            params.motor_par[ i + 8 ].runFrequency   = static_cast< quint16 >( _fengtouMotorCfgList[ i ].data[ 2 ].value * 1000 );
            if( _fengtouMotorCfgList[ i ].data[ 3 ].unit != "/") {
                params.motor_par[ i + 8 ].resetFrequency = static_cast< quint16 >( _fengtouMotorCfgList[ i ].data[ 3 ].value * 1000 );
            } else {
                params.motor_par[ i + 8 ].resetFrequency = 0xFFFF;
            }
            if( _fengtouMotorCfgList[ i ].data[ 4 ].unit != "/") {
                params.motor_par[ i + 8 ].runRange       = static_cast< quint16 >( _fengtouMotorCfgList[ i ].data[ 4 ].value );
            } else {
                params.motor_par[ i + 8 ].runRange       = 0xFFFF;
            }
            params.motor_par[ i + 8 ].direction      = static_cast< quint8 >( _fengtouMotorCfgList[ i ].data[ 5 ].value );
            if( _fengtouMotorCfgList[ i ].data[ 6 ].unit != "/") {
                params.motor_par[ i + 8 ].zeroPosition   = static_cast< quint8 >( _fengtouMotorCfgList[ i ].data[ 6 ].value );
            } else {
                params.motor_par[ i + 8 ].zeroPosition   = 0xFF;
            }
            if( _fengtouMotorCfgList[ i ].data[ 7 ].unit != "/") {
                params.motor_par[ i + 8 ].detectEnable   = static_cast< quint8 >( _fengtouMotorCfgList[ i ].data[ 7 ].value );
            } else {
                params.motor_par[ i + 8 ].detectEnable   = 0xFF;
            }
            params.motor_par[ i + 8 ].enable         = static_cast< quint8 >( _fengtouMotorCfgList[ i ].data[ 8 ].value );
        }
    }

    // 电机参数 - otherMotor
    for ( int i = 1; i <= _otherMotorCfgList.size(); i++ )
    {
        if ( _otherMotorCfgList.contains( i ) )
        {
            params.motor_par[ i + 16 ].runCurrent     = static_cast< quint16 >( _otherMotorCfgList[ i ].data[ 0 ].value * 10 );
            params.motor_par[ i + 16 ].fixedCurrent   = static_cast< quint16 >( _otherMotorCfgList[ i ].data[ 1 ].value * 10 );
            params.motor_par[ i + 16 ].runFrequency   = static_cast< quint16 >( _otherMotorCfgList[ i ].data[ 2 ].value * 1000 );
            if( _otherMotorCfgList[ i ].data[ 3 ].unit != "/") {
                params.motor_par[ i + 16 ].resetFrequency = static_cast< quint16 >( _otherMotorCfgList[ i ].data[ 3 ].value * 1000 );
            } else {
                params.motor_par[ i + 16 ].resetFrequency = 0xFFFF;
            }
            if( _otherMotorCfgList[ i ].data[ 4 ].unit != "/") {
                params.motor_par[ i + 16 ].runRange       = static_cast< quint16 >( _otherMotorCfgList[ i ].data[ 4 ].value );
            } else {
                params.motor_par[ i + 16 ].runRange       = 0xFFFF;
            }
            params.motor_par[ i + 16 ].direction      = static_cast< quint8 >( _otherMotorCfgList[ i ].data[ 5 ].value );
            if( _otherMotorCfgList[ i ].data[ 6 ].unit != "/") {
                params.motor_par[ i + 16 ].zeroPosition   = static_cast< quint8 >( _otherMotorCfgList[ i ].data[ 6 ].value );
            } else {
                params.motor_par[ i + 16 ].zeroPosition   = 0xFF;
            }
            if( _otherMotorCfgList[ i ].data[ 7 ].unit != "/") {
                params.motor_par[ i + 16 ].detectEnable   = static_cast< quint8 >( _otherMotorCfgList[ i ].data[ 7 ].value );
            } else {
                params.motor_par[ i + 16 ].detectEnable   = 0xFF;
            }
            params.motor_par[ i + 16 ].enable         = static_cast< quint8 >( _otherMotorCfgList[ i ].data[ 8 ].value );
        }
    }

    // 伺服电机参数
    if ( _servoList.contains( 1 ) )
    {
        params.servo_motor_par.overCurProtThres = static_cast< quint16 >( _servoList[ 1 ].value );
    }
    if ( _servoList.contains( 2 ) )
    {
        params.servo_motor_par.overVolProtThres = static_cast< quint16 >( _servoList[ 2 ].value );
    }
    if ( _servoList.contains( 3 ) )
    {
        params.servo_motor_par.underVolProtThres = static_cast< quint16 >( _servoList[ 3 ].value );
    }
    if ( _servoList.contains( 4 ) )
    {
        params.servo_motor_par.maxSpeedOfSpeedLoop = static_cast< quint16 >( _servoList[ 4 ].value );
    }
    if ( _servoList.contains( 5 ) )
    {
        params.servo_motor_par.accTime = static_cast< quint16 >( _servoList[ 5 ].value );
    }
    if ( _servoList.contains( 6 ) )
    {
        params.servo_motor_par.maxSpeedOfPosLoop = static_cast< quint16 >( _servoList[ 6 ].value );
    }
    if ( _servoList.contains( 7 ) )
    {
        params.servo_motor_par.servo_kp = static_cast< quint16 >( _servoList[ 7 ].value );
    }
    if ( _servoList.contains( 8 ) )
    {
        params.servo_motor_par.servo_ki = static_cast< quint16 >( _servoList[ 8 ].value );
    }

    return params;
}

UserParams ReadMachineFileConfig::makeUserParamFrame()
{
    UserParams params;
    memset( &params, 0, sizeof( params ) );
    // Populate params from _userCfgList
    if ( _userCfgList.contains( 1 ) )
        params.feng_auto = static_cast< quint8 >( _userCfgList[ 1 ].value );
    if ( _userCfgList.contains( 2 ) )
        params.poweroff_en = static_cast< quint8 >( _userCfgList[ 2 ].value );
    if ( _userCfgList.contains( 3 ) )
        params.speed_gain = static_cast< quint8 >( _userCfgList[ 3 ].value );
    if ( _userCfgList.contains( 4 ) )
        params.wg_xl = static_cast< quint8 >( _userCfgList[ 4 ].value );
    if ( _userCfgList.contains( 5 ) )
        params.lcd_timer = static_cast< quint8 >( _userCfgList[ 5 ].value );
    if ( _userCfgList.contains( 6 ) )
        params.auto_oil_en = static_cast< quint8 >( _userCfgList[ 6 ].value );
    if ( _userCfgList.contains( 7 ) )
        params.oil_model = static_cast< quint8 >( _userCfgList[ 7 ].value );
    if ( _userCfgList.contains( 8 ) )
        params.oil_timer = static_cast< quint8 >( _userCfgList[ 8 ].value );
    if ( _userCfgList.contains( 9 ) )
        params.oil_keep = static_cast< quint8 >( _userCfgList[ 9 ].value );
    if ( _userCfgList.contains( 10 ) )
        params.yi_w_timer = static_cast< quint8 >( _userCfgList[ 10 ].value );
    if ( _userCfgList.contains( 11 ) )
        params.sz_tui_pos = static_cast< quint8 >( _userCfgList[ 11 ].value );
    if ( _userCfgList.contains( 12 ) )
        params.start_x_timer = static_cast< quint8 >( _userCfgList[ 12 ].value );
    if ( _userCfgList.contains( 13 ) )
        params.jog_speed = static_cast< quint16 >( _userCfgList[ 13 ].value );
    if ( _userCfgList.contains( 14 ) )
        params.slow_speed = static_cast< quint16 >( _userCfgList[ 14 ].value );
    if ( _userCfgList.contains( 15 ) )
        params.mid_speed = static_cast< quint16 >( _userCfgList[ 15 ].value );
    if ( _userCfgList.contains( 16 ) )
        params.fast_speed = static_cast< quint16 >( _userCfgList[ 16 ].value );
    if ( _userCfgList.contains( 17 ) )
        params.jpan_speed = static_cast< quint8 >( _userCfgList[ 17 ].value );
    if ( _userCfgList.contains( 18 ) )
        params.xuan_xs1 = static_cast< quint16 >( _userCfgList[ 18 ].value );
    if ( _userCfgList.contains( 19 ) )
        params.xuan_xs2 = static_cast< quint16 >( _userCfgList[ 19 ].value );
    if ( _userCfgList.contains( 20 ) )
        params.midu_model = static_cast< quint8 >( _userCfgList[ 20 ].value );
    if ( _userCfgList.contains( 21 ) )
        params.qiu_model = static_cast< quint8 >( _userCfgList[ 21 ].value );
    if ( _userCfgList.contains( 22 ) )
        params.xj_low_speed = static_cast< quint8 >( _userCfgList[ 22 ].value );

    // Populate params from _otherCfgList
    if ( _otherCfgList.contains( 1 ) )
        params.yi_w_dir = static_cast< quint8 >( _otherCfgList[ 1 ].value );
    if ( _otherCfgList.contains( 2 ) )
        params.stop_m_timer = static_cast< quint16 >( _otherCfgList[ 2 ].value );
    if ( _otherCfgList.contains( 3 ) )
        params.start_m_timer = static_cast< quint8 >( _otherCfgList[ 3 ].value );
    if ( _otherCfgList.contains( 4 ) )
        params.stop_start_auto = static_cast< quint8 >( _otherCfgList[ 4 ].value );
    if ( _otherCfgList.contains( 5 ) )
        params.fast_res_stop = static_cast< quint8 >( _otherCfgList[ 5 ].value );
    if ( _otherCfgList.contains( 6 ) )
        params.lamp_auto = static_cast< quint8 >( _otherCfgList[ 6 ].value );
    if ( _otherCfgList.contains( 7 ) )
        params.xj_w_righ_pos = static_cast< quint16 >( _otherCfgList[ 7 ].value );
    if ( _otherCfgList.contains( 8 ) )
        params.xj_jian_pos = static_cast< quint16 >( _otherCfgList[ 8 ].value );
    if ( _otherCfgList.contains( 9 ) )
        params.tc_fan_pos = static_cast< quint16 >( _otherCfgList[ 9 ].value );
    if ( _otherCfgList.contains( 10 ) )
        params.xj_hua_tui = static_cast< quint8 >( _otherCfgList[ 10 ].value );
    if ( _otherCfgList.contains( 11 ) )
        params.xj_speed_model = static_cast< quint8 >( _otherCfgList[ 11 ].value );
    if ( _otherCfgList.contains( 12 ) )
        params.c_xuan_timer = static_cast< quint8 >( _otherCfgList[ 12 ].value );
    if ( _otherCfgList.contains( 13 ) )
        params.wg_md_ahead = static_cast< quint8 >( _otherCfgList[ 13 ].value );
    if ( _otherCfgList.contains( 14 ) )
        params.wg_md_leg = static_cast< quint8 >( _otherCfgList[ 14 ].value );
    if ( _otherCfgList.contains( 15 ) )
        params.feng_en = static_cast< quint8 >( _otherCfgList[ 15 ].value );
    if ( _otherCfgList.contains( 16 ) )
        params.anl_dao_open_leg = static_cast< quint8 >( _otherCfgList[ 16 ].value );
    if ( _otherCfgList.contains( 17 ) )
        params.anl_dao_open_pos = static_cast< quint16 >( _otherCfgList[ 17 ].value );
    if ( _otherCfgList.contains( 18 ) )
        params.anl_dao_close_leg = static_cast< quint8 >( _otherCfgList[ 18 ].value );
    if ( _otherCfgList.contains( 19 ) )
        params.anl_dao_close_pos = static_cast< quint16 >( _otherCfgList[ 19 ].value );
    if ( _otherCfgList.contains( 20 ) )
        params.start_zero_en = static_cast< quint8 >( _otherCfgList[ 20 ].value );
    if ( _otherCfgList.contains( 21 ) )
        params.feng_close_timer = static_cast< quint8 >( _otherCfgList[ 21 ].value );
    if ( _otherCfgList.contains( 22 ) )
        params.feng_zhi_zero = static_cast< quint8 >( _otherCfgList[ 22 ].value );
    if ( _otherCfgList.contains( 23 ) )
        params.f2_5msz_pos = static_cast< quint16 >( _otherCfgList[ 23 ].value );
    if ( _otherCfgList.contains( 24 ) )
        params.f2_5_ahead = static_cast< quint8 >( _otherCfgList[ 24 ].value );
    if ( _otherCfgList.contains( 25 ) )
        params.lamp_power = static_cast< quint8 >( _otherCfgList[ 25 ].value );
    if ( _otherCfgList.contains( 26 ) )
        params.yiwa_timer = static_cast< quint8 >( _otherCfgList[ 26 ].value );
    if ( _otherCfgList.contains( 27 ) )
        params.jt_yi_pos = static_cast< quint8 >( _otherCfgList[ 27 ].value );

    // 补充WarnSensor配置项
    for ( quint16 i = 0; i < 42; i++ )
    {
        if ( _fengtouWarnCfgList.contains( i ) )
        {
            params.seamHeadWarnCfg[ i ].id          = i;
            params.seamHeadWarnCfg[ i ].sensitivity = static_cast< quint16 >( _fengtouWarnCfgList.value( i ).sensitivity );
            params.seamHeadWarnCfg[ i ].direction   = static_cast< quint8 >( _fengtouWarnCfgList.value( i ).direction );
            params.seamHeadWarnCfg[ i ].enable      = _fengtouWarnCfgList.value( i ).enable ? 1 : 0;
        }
    }
    for ( quint16 i = 0; i < 50; i++ )
    {
        if ( _socketWarnCfgList.contains( i ) )
        {
            params.sockWarnCfg[ i ].id          = i;
            params.sockWarnCfg[ i ].sensitivity = static_cast< quint16 >( _socketWarnCfgList.value( i ).sensitivity );
            params.sockWarnCfg[ i ].direction   = static_cast< quint8 >( _socketWarnCfgList.value( i ).direction );
            params.sockWarnCfg[ i ].enable      = _socketWarnCfgList.value( i ).enable ? 1 : 0;
        }
    }

    return params;
}
