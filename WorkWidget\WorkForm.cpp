#include "WorkForm.h"
#include "ui_WorkForm.h"
#include <QMessageBox>

WorkForm::WorkForm( QWidget* parent, MainWidgetData* mainData, Communication* com, upInfoMessage* lastMessage, CommOperations* commOps )
    : QWidget( parent ), ui( new Ui::WorkForm ), mainData( mainData ), comm( com ), lastUpInfoMessage( lastMessage ), m_commOperations( commOps )
{
    ui->setupUi( this );

    initMainPage();
    initWarnPage();
    initInfoPage();
}

WorkForm::~WorkForm()
{
    delete ui;
}
