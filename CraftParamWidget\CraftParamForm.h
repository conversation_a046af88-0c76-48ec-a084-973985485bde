#ifndef CRAFTPARAMFORM_H
#define CRAFTPARAMFORM_H

#include "Common/defines.h"
#include "Common/parameters.h"
#include "CommonWidget/FileSelectForm.h"
#include "CommonWidget/comboform.h"
#include "CommonWidget/inputdialog.h"
#include "CommonWidget/letterinputdialog.h"
#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "Communicate/Communication.h"
#include "CoreBusiness/sokparser.h"
#include "FileWidget/FATParser.h"
#include <QButtonGroup>
#include <QDateTime>
#include <QDebug>
#include <QFileInfo>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QStandardItemModel>
#include <QTableWidget>
#include <QTextCodec>
#include <QVBoxLayout>
#include <QWidget>

namespace Ui
{
class CraftParamForm;
}

class CraftParamForm : public QWidget
{
    Q_OBJECT

public:
    explicit CraftParamForm( QWidget* parent = nullptr, MainWidgetData* mainData = nullptr, Communication* comm = nullptr );
    ~CraftParamForm();

    // 事件过滤器
    bool eventFilter( QObject* obj, QEvent* event ) override;

#pragma pack( 1 )
    struct FileInfoItem
    {
        QString fileName;
        QString fileSize;
        QString lastModified;
    };

    struct SpeedParam
    {
        quint16 speed;
        quint16 count;
        quint16 speed_new;
    };

    struct GraduationMotorParamItem
    {
        char    blockName[ 21 ];
        quint16 stepStart;
        quint16 stepEnd;
        char    graduationId;
        bool    prohibitsValueChange;
        quint16 cylinderStart[ 8 ];
        quint16 cylinderEnd[ 8 ];
    };

    struct ElasticMotorParamItem
    {
        char    blockName[ 21 ];
        quint16 stepStart;
        quint16 stepEnd;
        bool    prohibitsValueChange1;
        quint16 elasticStart1[ 8 ];
        quint16 elasticEnd1[ 8 ];
        bool    prohibitsValueChange2;
        quint16 elasticStart2[ 8 ];
        quint16 elasticEnd2[ 8 ];
        bool    prohibitsSawValueChange;
        quint16 sawStart[ 8 ];
        quint16 sawEnd[ 8 ];
    };

    struct SinkerMotorParamItem
    {
        char    blockName[ 21 ];
        quint16 stepStart;
        quint16 stepEnd;
        char    sinkerId;
        bool    prohibitsAngularValueChange;
        quint16 angularStart[ 8 ];
        quint16 angularEnd[ 8 ];
        bool    prohibitsSinkerValueChange;
        quint16 sinkerStart[ 8 ];
        quint16 sinkerEnd[ 8 ];
    };

    struct EconomizationParamItem
    {
        char    blockName[ 21 ];
        quint16 step_index;
        quint8  economizations[ 8 ];
        quint8  forStep;
    };

    struct ControlValeParamItem
    {
        char    blockName[ 21 ];
        quint16 step_index;
        quint16 position;
        char    movement;
        quint16 step;
    };

    struct RaisingDialMotorParamItem
    {
        char    blockName[ 21 ];
        quint16 step_index;
        quint16 position;
        quint8  motorId;
        char    movementCode;
        quint16 motorSteps;
    };

    struct CraftParam
    {
        QList< QSharedPointer< SpeedParam > >               speedParam;
        QList< QSharedPointer< GraduationMotorParamItem > > graduationMotorParam;
        QList< QSharedPointer< ElasticMotorParamItem > >    elasticMotorParam;
        QList< QSharedPointer< SinkerMotorParamItem > >     sinkerMotorParam;
        QList< QSharedPointer< EconomizationParamItem > > economizationParam;
        QList< QSharedPointer< ControlValeParamItem > > controlValeParam;
        QList< QSharedPointer< RaisingDialMotorParamItem > > raisingDialMotorParam;
    };

#pragma pack()

signals:
    void CraftParamFormToMainWinToShowSignal();  // 向主界面发送的显示主界面信号

private:
    Ui::CraftParamForm* ui;
    MainWidgetData*     mainData;
    Communication*      comm;
    ComboForm*          comboForm         = nullptr;
    NumberInputForm*    numberInputForm   = nullptr;
    LetterInputDialog*  letterInputDialog = nullptr;
    FileSelectForm*     fileSelectForm    = nullptr;
    InputDialog*        inputDialog       = nullptr;

    // 菜单页面
    QButtonGroup* menuBtnGroup;  // 菜单按键组

    // 工艺参数相关
    CraftParam craftParams;
    bool       isCraftPageInited = false;
    void       initCraftPage();
    int        currentSize       = 1;
    int        tableIndex        = -1;  // 标识是哪个table
    int        tableEditRowIndex = -1;  // 标识table的行
    int        tableEditColIndex = -1;  // 标识table的列
    void       saveCraftParams();

    // 分页相关变量
    static const int     ITEMS_PER_PAGE   = 10;  // 每页显示的项目数
    int                  currentSpeedPage = 0;   // 当前速度页
    int                  totalSpeedPages  = 0;   // 速度总页数
    QList< QLabel* >     speedLabels;            // 速度标签列表
    QList< QLabel* >     countLabels;            // 次数标签列表
    QList< MyLineEdit* > speedNewEdits;          // 新速度编辑框列表
    QList< QLabel* >     indexLabels;            // 序号标签列表

    // 毕业电机分页相关变量
    int                  currentGraduationPage = 0;  // 当前毕业电机页
    int                  totalGraduationPages  = 0;  // 毕业电机总页数
    QList< QLabel* >     graduationIndexLabels;      // 序号标签列表
    QList< QLabel* >     graduationNameLabels;       // 步骤名称标签列表
    QList< QLabel* >     graduationStepLabels;       // 步标签列表
    QList< MyLineEdit* > graduationIdEdits;          // ID编辑框列表
    QList< QLabel* >     graduationProhibitLabels;   // 禁止修改标签列表
    QList< QLabel* >     graduationStartLabels;      // 起始值标签列表
    QList< QLabel* >     graduationEndLabels;        // 结束值标签列表
    QList< MyLineEdit* > graduationStartNewEdits;    // 新起始值编辑框列表
    QList< MyLineEdit* > graduationEndNewEdits;      // 新结束值编辑框列表

    // 橡筋电机1分页相关变量
    int                  currentElastic1Page = 0;  // 当前橡筋电机1页
    int                  totalElastic1Pages  = 0;  // 橡筋电机1总页数
    QList< QLabel* >     elastic1IndexLabels;      // 序号标签列表
    QList< QLabel* >     elastic1NameLabels;       // 步骤名称标签列表
    QList< QLabel* >     elastic1StepLabels;       // 步标签列表
    QList< QLabel* >     elastic1ProhibitLabels;   // 禁止修改标签列表
    QList< QLabel* >     elastic1StartLabels;      // 起始值标签列表
    QList< QLabel* >     elastic1EndLabels;        // 结束值标签列表
    QList< MyLineEdit* > elastic1StartNewEdits;    // 新起始值编辑框列表
    QList< MyLineEdit* > elastic1EndNewEdits;      // 新结束值编辑框列表

    // 橡筋电机2分页相关变量
    int                  currentElastic2Page = 0;  // 当前橡筋电机2页
    int                  totalElastic2Pages  = 0;  // 橡筋电机2总页数
    QList< QLabel* >     elastic2IndexLabels;      // 序号标签列表
    QList< QLabel* >     elastic2NameLabels;       // 步骤名称标签列表
    QList< QLabel* >     elastic2StepLabels;       // 步标签列表
    QList< QLabel* >     elastic2ProhibitLabels;   // 禁止修改标签列表
    QList< QLabel* >     elastic2StartLabels;      // 起始值标签列表
    QList< QLabel* >     elastic2EndLabels;        // 结束值标签列表
    QList< MyLineEdit* > elastic2StartNewEdits;    // 新起始值编辑框列表
    QList< MyLineEdit* > elastic2EndNewEdits;      // 新结束值编辑框列表

    // 圆盘剪刀分页相关变量
    int                  currentSawPage = 0;  // 当前圆盘剪刀页
    int                  totalSawPages  = 0;  // 圆盘剪刀总页数
    QList< QLabel* >     sawIndexLabels;      // 序号标签列表
    QList< QLabel* >     sawNameLabels;       // 步骤名称标签列表
    QList< QLabel* >     sawStepLabels;       // 步标签列表
    QList< QLabel* >     sawProhibitLabels;   // 禁止修改标签列表
    QList< QLabel* >     sawStartLabels;      // 起始值标签列表
    QList< QLabel* >     sawEndLabels;        // 结束值标签列表
    QList< MyLineEdit* > sawStartNewEdits;    // 新起始值编辑框列表
    QList< MyLineEdit* > sawEndNewEdits;      // 新结束值编辑框列表

    // 生克密度分页相关变量
    int                  currentSinkerPage = 0;  // 当前生克密度页
    int                  totalSinkerPages  = 0;  // 生克密度总页数
    QList< QLabel* >     sinkerIndexLabels;      // 序号标签列表
    QList< QLabel* >     sinkerNameLabels;       // 步骤名称标签列表
    QList< QLabel* >     sinkerStepLabels;       // 步标签列表
    QList< MyLineEdit* > sinkerIdEdits;          // ID编辑框列表
    QList< QLabel* >     sinkerProhibitLabels;   // 禁止修改标签列表
    QList< QLabel* >     sinkerStartLabels;      // 起始值标签列表
    QList< QLabel* >     sinkerEndLabels;        // 结束值标签列表
    QList< MyLineEdit* > sinkerStartNewEdits;    // 新起始值编辑框列表
    QList< MyLineEdit* > sinkerEndNewEdits;      // 新结束值编辑框列表

    // 生克罩摆动分页相关变量
    int                  currentSinkerAngularPage = 0;  // 当前生克罩摆动页
    int                  totalSinkerAngularPages  = 0;  // 生克罩摆动总页数
    QList< QLabel* >     sinkerAngularIndexLabels;      // 序号标签列表
    QList< QLabel* >     sinkerAngularNameLabels;       // 步骤名称标签列表
    QList< QLabel* >     sinkerAngularStepLabels;       // 步标签列表
    QList< MyLineEdit* > sinkerAngularIdEdits;          // ID编辑框列表
    QList< QLabel* >     sinkerAngularProhibitLabels;   // 禁止修改标签列表
    QList< QLabel* >     sinkerAngularStartLabels;      // 起始值标签列表
    QList< QLabel* >     sinkerAngularEndLabels;        // 结束值标签列表
    QList< MyLineEdit* > sinkerAngularStartNewEdits;    // 新起始值编辑框列表
    QList< MyLineEdit* > sinkerAngularEndNewEdits;      // 新结束值编辑框列表

    // 节约数据分页相关变量
    int                  currentEconomizationPage = 0;  // 当前节约数据页
    int                  totalEconomizationPages  = 0;  // 节约数据总页数
    QList< QLabel* >     economizationIndexLabels;      // 序号标签列表
    QList< QLabel* >     economizationNameLabels;       // 步骤名称标签列表
    QList< QLabel* >     economizationStepLabels;       // Step标签列表
    QList< QLabel* >     economizationOldLabels;        // Old标签列表
    QList< MyLineEdit* > economizationNewEdits;         // New编辑框列表

    // 控制阀分页相关变量
    int                  currentControlValvePage = 0;  // 当前控制阀页
    int                  totalControlValvePages  = 0;  // 控制阀总页数
    QList< QLabel* >     controlValveIndexLabels;      // 序号标签列表
    QList< QLabel* >     controlValveNameLabels;       // 步骤名称标签列表
    QList< QLabel* >     controlValveStepLabels;       // 步标签列表
    QList< QLabel* >     controlValvePositionLabels;   // 角度标签列表
    QList< QLabel* >     controlValveMovementLabels;   // 类型标签列表
    QList< QLabel* >     controlValveOldLabels;        // Old标签列表
    QList< MyLineEdit* > controlValveNewEdits;         // New编辑框列表

    // USB
    bool                   isUSBTransferPageInited  = false;
    QList< FileInfoItem >* CFTFileList              = nullptr;
    QList< FileInfoItem >* CFTUSBFileList           = nullptr;
    QStandardItemModel*    _db_cft_table_this_model = nullptr;
    QStandardItemModel*    _db_cft_table_usb_model  = nullptr;
    QString                currentCFTFile           = "";
    QString                currentCFTThisFile       = "";
    QString                currentCFTUSBFile        = "";
    QString                CFTUSBBaseAddr           = "";
    void                   refreshCFTFileList( QString fileCFTbaseAddr );
    void                   refreshCFTUSBFileList( QString fileCFTbaseAddr );
    void                   initUSBCFTTransferPage();
    void                   initCFTFileThisTableView();
    void                   initCFTFileUSBTableView();
    QString                checkCFTUSBDirExists();

private slots:
    void onHomeBtnClicked();               // 测试界面返回 主菜单键按下槽函数
    void onMenuBtnGroupClicked( int id );  // 菜单按键组按下槽函数

    // Craft
    void showCraftParams();
    void showCraftSpeed();
    void showCraftElastic();
    void showCraftGraduation();
    void showCraftSinker();
    void showCraftEconomization();
    void showCraftControlValve();
    void onNumberInputFormFinished( QString value );

    // 辅助函数：根据stepIndex从moduleDataVector获取步段名称
    QString getBlockNameFromModuleData( int stepIndex, const QVector< QSharedPointer< SokParser::ModuleDataItem > >& moduleDataVector );
    // 辅助函数：将步段名称设置到item的blockName字段
    void setBlockNameToItem( char* blockName, const QString& blockNameText );
    void onFileNameClicked( QString name );
    void onLetterInputDialogFinished( QString text );
    void onSpeedIncClicked();
    void onSpeedDecClicked();

    // 分页相关方法
    void initSpeedFrame();                        // 初始化速度框架
    void initGraduationFrame();                   // 初始化毕业电机框架
    void initElastic1Frame();                     // 初始化橡筋电机1框架
    void initElastic2Frame();                     // 初始化橡筋电机2框架
    void initSawFrame();                          // 初始化圆盘剪刀框架
    void initSinkerFrame();                       // 初始化生克密度框架
    void initSinkerAngularFrame();                // 初始化生克罩摆动框架
    void initEconomizationFrame();                // 初始化节约数据框架
    void initControlValveFrame();                 // 初始化控制阀框架
    void updateSpeedPage();                       // 更新速度页面
    void updateGraduationPage();                  // 更新毕业电机页面
    void updateElastic1Page();                    // 更新橡筋电机1页面
    void updateElastic2Page();                    // 更新橡筋电机2页面
    void updateSawPage();                         // 更新圆盘剪刀页面
    void updateSinkerPage();                      // 更新生克密度页面
    void updateSinkerAngularPage();               // 更新生克罩摆动页面
    void updateEconomizationPage();               // 更新节约数据页面
    void updateControlValvePage();                // 更新控制阀页面
    void onPrevPageClicked();                     // 上一页按钮点击
    void onNextPageClicked();                     // 下一页按钮点击
    void onSpeedEditFinished( QString value );    // 速度编辑完成
    void updatePageButtonStatus();                // 更新翻页按钮状态
    void onTabWidgetCurrentChanged( int index );  // 标签页切换事件
};

#endif  // CRAFTPARAMFORM_H
