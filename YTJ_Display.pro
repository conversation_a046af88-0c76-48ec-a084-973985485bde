#-------------------------------------------------
#
# Project created by QtCreator 2024-01-15T14:40:49
#
#-------------------------------------------------

QT       += core gui serialport network mqtt serialbus

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = YTJ_Display
TEMPLATE = app

# The following define makes your compiler emit warnings if you use
# any feature of Qt which has been marked as deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if you use deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

CONFIG += c++11

LIBS += -lpthread

INCLUDEPATH +=$$PWD/TestWidget \

SOURCES += \
    Common/CommOperations.cpp \
    CommonWidget/CommonWarnDialog.cpp \
    CommonWidget/EyeWarnView.cpp \
    CommonWidget/FileSelectForm.cpp \
    CommonWidget/SelfCheckForm.cpp \
    CommonWidget/WaveModeForm.cpp \
    CommonWidget/comboform.cpp \
    CommonWidget/floatinputform.cpp \
    CommonWidget/mylineedit.cpp \
    CommonWidget/numberinputform.cpp \
    CommonWidget/letterinputdialog.cpp \
    Communicate/CCycleBuffer.cpp \
    Communicate/CommCan.cpp \
    Communicate/Communication.cpp \
    Communicate/commkeyboard.cpp \
    Communicate/crc_16.c \
    Communicate/keyboardBeep.cpp \
    Config/ConfigManager.cpp \
    Config/ReadOnekeyConfig.cpp \
    Config/readCfg.cpp \
    Config/readResetConfig.cpp \
    Config/readWarnConfig.cpp \
    Config/readlinkconfig.cpp \
    Config/readmachinefileconfig.cpp \
    Config/readtestconfig.cpp \
    CoreBusiness/SokBusiness.cpp \
    CraftParamWidget/CFTUSBFileTransferPage.cpp \
    CraftParamWidget/ControlValveFrame.cpp \
    CraftParamWidget/CraftPage.cpp \
    CraftParamWidget/CraftParamForm.cpp \
    CraftParamWidget/EconomizationFrame.cpp \
    CraftParamWidget/RaisingDialMotorFrame.cpp \
    OneKeyWidget/EEyePage.cpp \
    CraftParamWidget/ElasticFrame.cpp \
    CraftParamWidget/ElasticFrame2.cpp \
    OneKeyWidget/EyeParaSetForm.cpp \
    CraftParamWidget/GraduationFrame.cpp \
    CraftParamWidget/SawFrame.cpp \
    CraftParamWidget/SinkerAngularFrame.cpp \
    CraftParamWidget/SinkerFrame.cpp \
    CraftParamWidget/SpeedFrame.cpp \
    FileWidget/ActionViewPage.cpp \
    FileWidget/ChainDataPage.cpp \
    FileWidget/FATDecode.cpp \
    FileWidget/FATParser.cpp \
    FileWidget/FengtouPage.cpp \
    FileWidget/FileAdminPage.cpp \
    FileWidget/FlowerAndLamaoLoopDialog.cpp \
    FileWidget/NeedleEditPage.cpp \
    FileWidget/NeedleView.cpp \
    FileWidget/PatternPage.cpp \
    FileWidget/USBFileTransferPage.cpp \
    FileWidget/Zhusuo2CfgDialog.cpp \
    FileWidget/ZhusuoCfgDialog.cpp \
    FileWidget/densityform.cpp \
    FileWidget/fileform.cpp \
    FileWidget/needitemcopyform.cpp \
    FileWidget/needleitemview.cpp \
    FileWidget/valveeditform.cpp \
    LogWidget/LogForm.cpp \
    LogWidget/NetworkPage.cpp \
    Mqtt/mqttclient.cpp \
    Mqtt/uppermsgmaker.cpp \
    OneKeyWidget/KtfPage.cpp \
    OneKeyWidget/OneKeyForm.cpp \
    ParasetWidget/BasicParaset.cpp \
    ParasetWidget/FastResetParaset.cpp \
    ParasetWidget/FengtouCalParaset.cpp \
    ParasetWidget/FengtouMotoParaset.cpp \
    ParasetWidget/FengtouParaset.cpp \
    OneKeyWidget/FengtouWarnParaset.cpp \
    ParasetWidget/NeedleParaset.cpp \
    ParasetWidget/OtherMotorParaset.cpp \
    OneKeyWidget/OtherParaset.cpp \
    ParasetWidget/PeripheralParaset.cpp \
    ParasetWidget/PositionParaset.cpp \
    ParasetWidget/Position2Paraset.cpp \
    ParasetWidget/SocketMotorParaset.cpp \
    OneKeyWidget/SocketWarnParaset.cpp \
    OneKeyWidget/UserParaset.cpp \
    LogWidget/WarnLogPage.cpp \
    SensorWidget/SensorForm.cpp \
    TestWidget/ComboCheckDialog.cpp \
    SensorWidget/SensorTestpage.cpp \
    TestWidget/ServZeroDialog.cpp \
    TestWidget/ValveSelfCheckDialog.cpp \
    TestWidget/test_fengtoutest.cpp \
    TestWidget/test_function.cpp \
    TestWidget/test_keyboard.cpp \
    TestWidget/test_servo.cpp \
    TestWidget/test_servoPara.cpp \
    TestWidget/test_steptest.cpp \
    UsbWidget/UsbAdminPage.cpp \
    UsbWidget/UsbForm.cpp \
    Util/logmanager.cpp \
    CoreBusiness/sokparser.cpp \
    WorkWidget/InfoPage.cpp \
    WorkWidget/MainPage.cpp \
    WorkWidget/RunningPowerDialog.cpp \
    WorkWidget/ShowWarnPage.cpp \
    WorkWidget/WorkForm.cpp \
    CommonWidget/inputdialog.cpp \
    main.cpp \
    Widget/widget.cpp \
    ParasetWidget/parasetform.cpp \
    TestWidget/test_selecttest.cpp \
    TestWidget/test_valvetest.cpp \
    TestWidget/testform.cpp

HEADERS += \
    Common/CommOperations.h \
    CommonWidget/CommonWarnDialog.h \
    CommonWidget/EyeWarnView.h \
    CommonWidget/SelfCheckForm.h \
    CommonWidget/WaveModeForm.h \
    Communicate/CommCan.h \
    Communicate/circleDataProtocol.h \
    Common/defines.h \
    Common/parameters.h \
    CommonWidget/FileSelectForm.h \
    CommonWidget/MyWidget.h \
    CommonWidget/clickablelabel.h \
    CommonWidget/comboform.h \
    CommonWidget/floatinputform.h \
    CommonWidget/mylineedit.h \
    CommonWidget/numberinputform.h \
    CommonWidget/letterinputdialog.h \
    Communicate/CCycleBuffer.h \
    Communicate/Communication.h \
    Communicate/commkeyboard.h \
    Communicate/crc_16.h \
    Communicate/keyboardBeep.h \
    Communicate/parasetProtocol.h \
    Communicate/upInfoMessage.h \
    Config/ConfigManager.h \
    Config/ReadOnekeyConfig.h \
    Config/readCfg.h \
    Config/readResetConfig.h \
    Config/readWarnConfig.h \
    Config/readlinkconfig.h \
    Config/readmachinefileconfig.h \
    Config/readtestconfig.h \
    CoreBusiness/SokBusiness.h \
    CraftParamWidget/CraftParamForm.h \
    OneKeyWidget/EyeParaSetForm.h \
    FileWidget/ColorBlockWidget.h \
    FileWidget/FATDecode.h \
    FileWidget/FATParser.h \
    CommonWidget/MyPlainTextEdit.h \
    FileWidget/FlowerAndLamaoLoopDialog.h \
    FileWidget/NeedleView.h \
    FileWidget/PatternViewer.h \
    FileWidget/Zhusuo2CfgDialog.h \
    FileWidget/ZhusuoCfgDialog.h \
    FileWidget/densityform.h \
    FileWidget/fileform.h \
    FileWidget/needitemcopyform.h \
    FileWidget/needleitemview.h \
    FileWidget/valveeditform.h \
    LogWidget/LogForm.h \
    OneKeyWidget/OneKeyForm.h \
    RunWidget/CountingDialog.h \
    RunWidget/HandImageWidget.h \
    RunWidget/MachineInfo.h \
    Mqtt/mqttclient.h \
    Mqtt/uppermsgmaker.h \
    RunWidget/SelectorView.h \
    SensorWidget/SensorForm.h \
    TestWidget/CircleButton.h \
    TestWidget/ComboCheckDialog.h \
    TestWidget/ServZeroDialog.h \
    TestWidget/ValveSelfCheckDialog.h \
    TestWidget/test_steptest.h \
    UsbWidget/UsbForm.h \
    Util/logmanager.h \
    CoreBusiness/sokparser.h \
    WorkWidget/RunningPowerDialog.h \
    WorkWidget/WorkForm.h \
    CommonWidget/inputdialog.h \
    Widget/widget.h \
    ParasetWidget/parasetform.h \
    TestWidget/testform.h


FORMS += \
    CommonWidget/CommonWarnDialog.ui \
    CommonWidget/FileSelectForm.ui \
    CommonWidget/SelfCheckForm.ui \
    CommonWidget/WaveModeForm.ui \
    CommonWidget/comboform.ui \
    CommonWidget/floatinputform.ui \
    CommonWidget/inputdialog.ui \
    CommonWidget/numberinputform.ui \
    CommonWidget/letterinputdialog.ui \
    CraftParamWidget/CraftParamForm.ui \
    OneKeyWidget/EyeParaSetForm.ui \
    FileWidget/FlowerAndLamaoLoopDialog.ui \
    FileWidget/Zhusuo2CfgDialog.ui \
    FileWidget/ZhusuoCfgDialog.ui \
    FileWidget/densityform.ui \
    FileWidget/needitemcopyform.ui \
    FileWidget/valveeditform.ui \
    LogWidget/LogForm.ui \
    OneKeyWidget/OneKeyForm.ui \
    ParasetWidget/parasetform.ui \
    SensorWidget/SensorForm.ui \
    TestWidget/ComboCheckDialog.ui \
    TestWidget/ServZeroDialog.ui \
    TestWidget/ValveSelfCheckDialog.ui \
    UsbWidget/UsbForm.ui \
    WorkWidget/RunningPowerDialog.ui \
    WorkWidget/WorkForm.ui \
    Widget/widget.ui \
    TestWidget/testform.ui \
    FileWidget/fileform.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

RESOURCES += \
    icon/icon.qrc
