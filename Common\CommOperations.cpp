#include "CommOperations.h"
#include <QDebug>
#include <QMessageBox>
#include <QThread>

CommOperations::CommOperations( QObject* parent ) : QObject( parent )
{
    // 初始化机器参数发送定时器
    m_machineParamSendTimer.setSingleShot( true );
    connect( &m_machineParamSendTimer, &QTimer::timeout, this, &CommOperations::onMachineParamSendTimerTimeout );

    // 初始化用户参数发送定时器
    m_userParamSendTimer.setSingleShot( true );
    connect( &m_userParamSendTimer, &QTimer::timeout, this, &CommOperations::onUserParamSendTimerTimeout );

    // 初始化织袜复位动作发送定时器
    m_sockResetSendTimer.setSingleShot( true );
    connect( &m_sockResetSendTimer, &QTimer::timeout, this, &CommOperations::onSockResetSendTimerTimeout );

    // 初始化步骤数据发送定时器
    m_stepDataSendTimer.setSingleShot( true );
    connect( &m_stepDataSendTimer, &QTimer::timeout, this, &CommOperations::onStepDataSendTimerTimeout );
}

CommOperations::~CommOperations()
{
    // 停止定时器
    m_machineParamSendTimer.stop();
    m_userParamSendTimer.stop();
    m_sockResetSendTimer.stop();
    m_stepDataSendTimer.stop();
}

void CommOperations::init( Communication* comm, MainWidgetData* mainData )
{
    m_comm     = comm;
    m_mainData = mainData;

    // 连接通信对象的信号
    if ( m_comm )
    {
        connect( m_comm, &Communication::MachineParamFrameReceived, this, [this]( qint16 size, quint8* data ) {
            if ( size > 1 )
            {
                if ( data[ 0 ] & 0x80 )
                {
                    m_machineParamSendSuccess = true;
                }
            }
        } );

        connect( m_comm, &Communication::UserParamFrameReceived, this, [this]( qint16 size, quint8* data ) {
            if ( size > 1 )
            {
                if ( data[ 0 ] & 0x80 )
                {
                    m_userParamSendSuccess = true;
                }
            }
        } );

        // 连接织袜复位动作响应信号
        connect( m_comm, &Communication::StepDataFrameReceived, this, [this]( qint16 size, quint8* data ) {
            // 如果当前状态是织袜复位状态
            if ( size > 1 )
            {
                if ( data[ 0 ] & 0x80 )
                {
                    if ( m_mainData->machineState == MachineState::SockReset )
                    {
                        m_sockResetSendSuccess = true;
                    }
                    else if ( m_mainData->machineState == MachineState::Running )
                    {
                        // 普通步骤数据发送成功
                        m_stepDataSendSuccess = true;
                    }
                }
            }
        } );

        connect( m_comm, &Communication::CommandFrameReceived, this, [this]( qint16 size, quint8* data ) {
            if ( size > 1 )
            {
                if ( data[ 0 ] & 0x20 )
                {
                    if ( m_mainData->machineState == MachineState::SockReset )
                    {
                        sendSockResetAction( false );
                    }
                }
            }
        } );
    }
}

void CommOperations::sendMachineParam()
{
    if ( !m_comm || !m_mainData || !m_mainData->readMachineFileConfig )
    {
        qDebug() << "Communication object or machine configuration not initialized";
        return;
    }

    MachineParams machineParams = m_mainData->readMachineFileConfig->makeMachineParamFrame();
    // 创建数组，将machineParams的值赋值给数组，然后通过comm->pushDataTobuffer()发送
    quint8 buffer[ sizeof( MachineParams ) ];
    memcpy( buffer, &machineParams, sizeof( MachineParams ) );
    m_comm->pushDataTobuffer( 0x01, buffer, sizeof( MachineParams ) );
}

void CommOperations::sendUserParam()
{
    if ( !m_comm || !m_mainData || !m_mainData->readMachineFileConfig )
    {
        qDebug() << "Communication object or machine configuration not initialized";
        return;
    }

    UserParams userParams = m_mainData->readMachineFileConfig->makeUserParamFrame();
    // 创建数组，将userParams的值赋值给数组，然后通过comm->pushDataTobuffer()发送
    quint8 buffer[ sizeof( UserParams ) ];
    memcpy( buffer, &userParams, sizeof( UserParams ) );
    m_comm->pushDataTobuffer( 0x02, buffer, sizeof( UserParams ) );
}

void CommOperations::startSendParams()
{
    // 重置状态
    m_machineParamSendSuccess = false;
    m_retryCountMachineParam  = 0;
    m_userParamSendSuccess    = false;
    m_retryCountUserParam     = 0;

    // 发送机器参数
    sendMachineParam();
    m_machineParamSendTimer.start( RETRY_INTERVAL );

    // 发送用户参数
    sendUserParam();
    m_userParamSendTimer.start( RETRY_INTERVAL );
}

void CommOperations::onMachineParamSendTimerTimeout()
{
    if ( m_machineParamSendSuccess )
    {
        qDebug() << "MachineParam frame sent successfully.";
        m_retryCountMachineParam = 0;  // 发送成功，重置重发计数
        emit machineParamSendSuccessful();
    }
    else
    {
        qDebug() << "Failed to send MachineParam frame.";
        sendMachineParam();
        m_retryCountMachineParam++;
        if ( m_retryCountMachineParam >= MAX_RETRIES )
        {
            qDebug() << "Failed to send MachineParam frame after" << MAX_RETRIES << "retries.";
            m_retryCountMachineParam = 0;
            emit machineParamSendFailed();
            return;
        }
        qDebug() << "Retrying in" << RETRY_INTERVAL << "milliseconds...";
        m_machineParamSendTimer.start( RETRY_INTERVAL );  // 重新开始定时器
    }
}

void CommOperations::onUserParamSendTimerTimeout()
{
    if ( m_userParamSendSuccess )
    {
        qDebug() << "UserParam frame sent successfully.";
        m_retryCountUserParam = 0;  // 发送成功，重置重发计数
        emit userParamSendSuccessful();
    }
    else
    {
        qDebug() << "Failed to send UserParam frame.";
        sendUserParam();
        m_retryCountUserParam++;
        if ( m_retryCountUserParam >= MAX_RETRIES )
        {
            qDebug() << "Failed to send UserParam frame after" << MAX_RETRIES << "retries.";
            m_retryCountUserParam = 0;
            emit userParamSendFailed();
            return;
        }
        qDebug() << "Retrying in" << RETRY_INTERVAL << "milliseconds...";
        m_userParamSendTimer.start( RETRY_INTERVAL );  // 重新开始定时器
    }
}

// 发送织袜复位动作
void CommOperations::sendSockResetAction( bool isFirstSend )
{
    // 使用static变量替代全局变量
    static int currentResetCircle = 0;

    // 如果是第一次发送，重置圈计数和重试计数
    if ( isFirstSend )
    {
        currentResetCircle       = 0;
        m_retryCountSockReset    = 0;
        m_sockResetSendSuccess   = false;
        m_mainData->machineState = MachineState::SockReset;
        qDebug() << "Reset circle count has been reset";
    }

    // 确保mainData和readResetConfig已初始化
    if ( !m_comm || !m_mainData || !m_mainData->readResetConfig )
    {
        qDebug() << "Communication object or reset configuration not initialized";
        emit sockResetSendFailed();
        return;
    }

    // 获取复位动作列表
    QVector< QVector< ReadResetConfig::SockResetActionStruct > >& resetList = m_mainData->readResetConfig->getResetList( ReadResetConfig::SOCK_RESET );

    // 检查是否已完成所有圈的发送
    if ( currentResetCircle >= resetList.size() )
    {
        qDebug() << "All reset actions have been sent";
        currentResetCircle = 0;
        emit sockResetSendSuccessful();
        m_mainData->machineState = MachineState::Idle;
        return;
    }

    // 获取当前圈的动作数组
    QVector< ReadResetConfig::SockResetActionStruct >& circleActions = resetList[ currentResetCircle ];
    qDebug() << "Sending circle" << ( currentResetCircle + 1 ) << "reset actions, total" << circleActions.size() << "actions";

    // 创建CircleData结构体并初始化
    CircleData circleData;
    memset( &circleData, 0, sizeof( CircleData ) );

    // 设置基本信息
    circleData.circle_id   = currentResetCircle;
    circleData.circle_type = 0;  // 假设复位动作的圈类型为0
    circleData.direction   = 0;  // 假设复位动作的编织方向为0

    // 初始化气阀计数
    circleData.valve_count = 0;

    // 遍历当前圈的所有动作并处理
    for ( int i = 0; i < circleActions.size(); i++ )
    {
        ReadResetConfig::SockResetActionStruct& action = circleActions[ i ];

        int id       = action.id;
        int position = action.position;
        int type     = action.type;
        int value    = action.value;

        // 根据动作类型处理不同的命令
        if ( type == 0 )  // 气阀类型
        {
            // 确保不超过气阀动作数组大小
            if ( circleData.valve_count < 80 )
            {
                circleData.valve_actions[ circleData.valve_count ].valve_id    = id;
                circleData.valve_actions[ circleData.valve_count ].angle       = position;
                circleData.valve_actions[ circleData.valve_count ].valve_state = value;
                circleData.valve_count++;

                qDebug() << "Added valve reset action: ID=" << id << ", Position=" << position << ", Value=" << value;
            }
            else
            {
                qDebug() << "Valve action count exceeds limit, ignored: ID=" << id;
            }
        }
        else if ( type == 1 )  // 密度电机类型
        {
            // 确保id在有效范围内
            if ( id < 16 )  // 假设motor_data数组大小为16
            {
                circleData.motor_data[ id ].position = position;
                circleData.motor_data[ id ].value    = value;

                qDebug() << "Added density motor reset action: ID=" << id << ", Position=" << position << ", Value=" << value;
            }
            else
            {
                qDebug() << "Density motor ID out of range, ignored: ID=" << id;
            }
        }
        else if ( type == 2 )  // 伺服电机类型
        {
            circleData.main_motor.start_pos = position;
            circleData.main_motor.speed     = value;

            qDebug() << "Added servo motor reset action: ID=" << id << ", Position=" << position << ", Value=" << value;
        }
    }

    // 创建buffer并复制数据
    uint8_t* buffer = new uint8_t[ sizeof( CircleData ) ];
    memcpy( buffer, &circleData, sizeof( CircleData ) );

    // 发送数据
    m_comm->pushDataTobuffer( 0x03, buffer, sizeof( CircleData ) );

    // 释放内存
    delete[] buffer;

    // 重置发送成功标志
    m_sockResetSendSuccess = false;

    // 启动定时器等待响应
    m_sockResetSendTimer.start( RETRY_INTERVAL );
}

void CommOperations::onSockResetSendTimerTimeout()
{
    if ( m_sockResetSendSuccess )
    {
        qDebug() << "SockReset frame sent successfully.";
        m_retryCountSockReset = 0;  // 发送成功，重置重发计数
    }
    else
    {
        qDebug() << "Failed to send SockReset frame.";
        m_retryCountSockReset++;
        if ( m_retryCountSockReset >= MAX_RETRIES )
        {
            qDebug() << "Failed to send SockReset frame after" << MAX_RETRIES << "retries.";
            m_retryCountSockReset = 0;
            emit sockResetSendFailed();
            return;
        }
        qDebug() << "Retrying in" << RETRY_INTERVAL << "milliseconds...";
        // 重新发送当前圈
        sendSockResetAction( false );
        m_sockResetSendTimer.start( RETRY_INTERVAL );  // 重新开始定时器
    }
}

// 发送步骤数据
void CommOperations::sendStepData( CircleData* circleData )
{
    if ( !m_comm || !circleData )
    {
        qDebug() << "Communication object or circle data not initialized";
        emit stepDataSendFailed();
        return;
    }

    // 保存当前步骤数据的指针
    m_currentCircleData = circleData;

    // 重置状态
    m_stepDataSendSuccess = false;
    m_retryCountStepData  = 0;

    // 创建buffer并复制数据
    uint8_t* buffer = new uint8_t[ sizeof( CircleData ) ];
    memcpy( buffer, circleData, sizeof( CircleData ) );

    // 发送数据
    m_comm->pushDataTobuffer( 0x03, buffer, sizeof( CircleData ) );

    // 释放内存
    delete[] buffer;

    qDebug() << "Sending step data for circle ID:" << circleData->circle_id << "size:" << sizeof( CircleData );

    // 启动定时器等待响应
    m_stepDataSendTimer.start( RETRY_INTERVAL );
}

void CommOperations::onStepDataSendTimerTimeout()
{
    if ( m_stepDataSendSuccess )
    {
        qDebug() << "Step data frame sent successfully.";
        m_retryCountStepData = 0;  // 发送成功，重置重发计数
        emit stepDataSendSuccessful();
    }
    else
    {
        qDebug() << "Failed to send step data frame.";
        m_retryCountStepData++;
        if ( m_retryCountStepData >= MAX_RETRIES )
        {
            qDebug() << "Failed to send step data frame after" << MAX_RETRIES << "retries.";
            m_retryCountStepData = 0;
            emit stepDataSendFailed();
            return;
        }
        qDebug() << "Retrying in" << RETRY_INTERVAL << "milliseconds...";

        // 重新发送当前步骤数据
        if ( m_currentCircleData )
        {
            // 创建buffer并复制数据
            uint8_t* buffer = new uint8_t[ sizeof( CircleData ) ];
            memcpy( buffer, m_currentCircleData, sizeof( CircleData ) );

            // 发送数据
            m_comm->pushDataTobuffer( 0x03, buffer, sizeof( CircleData ) );

            // 释放内存
            delete[] buffer;
        }

        // 重新开始定时器
        m_stepDataSendTimer.start( RETRY_INTERVAL );
    }
}
