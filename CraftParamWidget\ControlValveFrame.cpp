#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"
#include <QGridLayout>
#include <QLabel>
// #include <QTextCodec>  // 暂时注释，使用备选方案

// 初始化控制阀框架
void CraftParamForm::initControlValveFrame()
{
    // 清空之前的控件列表
    if (!controlValveIndexLabels.isEmpty()) {
        for (auto label : controlValveIndexLabels) {
            delete label;
        }
        controlValveIndexLabels.clear();
    }
    if (!controlValveNameLabels.isEmpty()) {
        for (auto label : controlValveNameLabels) {
            delete label;
        }
        controlValveNameLabels.clear();
    }
    if (!controlValveStepLabels.isEmpty()) {
        for (auto label : controlValveStepLabels) {
            delete label;
        }
        controlValveStepLabels.clear();
    }
    if (!controlValvePositionLabels.isEmpty()) {
        for (auto label : controlValvePositionLabels) {
            delete label;
        }
        controlValvePositionLabels.clear();
    }
    if (!controlValveMovementLabels.isEmpty()) {
        for (auto label : controlValveMovementLabels) {
            delete label;
        }
        controlValveMovementLabels.clear();
    }
    if (!controlValveOldLabels.isEmpty()) {
        for (auto label : controlValveOldLabels) {
            delete label;
        }
        controlValveOldLabels.clear();
    }
    if (!controlValveNewEdits.isEmpty()) {
        for (auto edit : controlValveNewEdits) {
            delete edit;
        }
        controlValveNewEdits.clear();
    }

    // 创建网格布局
    QGridLayout* layout = new QGridLayout(ui->controlValveFrame);

    // 创建标题行
    QLabel* indexHeader = new QLabel("序号", ui->controlValveFrame);
    QLabel* nameHeader = new QLabel("步骤名称", ui->controlValveFrame);
    QLabel* stepHeader = new QLabel("步", ui->controlValveFrame);
    QLabel* positionHeader = new QLabel("角度", ui->controlValveFrame);
    QLabel* movementHeader = new QLabel("类型", ui->controlValveFrame);
    QLabel* oldHeader = new QLabel("Old", ui->controlValveFrame);
    QLabel* newHeader = new QLabel("New", ui->controlValveFrame);

    // 设置标题样式
    QString headerStyle = "QLabel { background-color: #3498db; color: white; font-weight: bold; border-radius: 4px; padding: 4px; }";
    indexHeader->setStyleSheet(headerStyle);
    nameHeader->setStyleSheet(headerStyle);
    stepHeader->setStyleSheet(headerStyle);
    positionHeader->setStyleSheet(headerStyle);
    movementHeader->setStyleSheet(headerStyle);
    oldHeader->setStyleSheet(headerStyle);
    newHeader->setStyleSheet(headerStyle);
    indexHeader->setAlignment(Qt::AlignCenter);
    nameHeader->setAlignment(Qt::AlignCenter);
    stepHeader->setAlignment(Qt::AlignCenter);
    positionHeader->setAlignment(Qt::AlignCenter);
    movementHeader->setAlignment(Qt::AlignCenter);
    oldHeader->setAlignment(Qt::AlignCenter);
    newHeader->setAlignment(Qt::AlignCenter);

    // 添加标题到布局
    layout->addWidget(indexHeader, 0, 0);
    layout->addWidget(nameHeader, 0, 1);
    layout->addWidget(stepHeader, 0, 2);
    layout->addWidget(positionHeader, 0, 3);
    layout->addWidget(movementHeader, 0, 4);
    layout->addWidget(oldHeader, 0, 5);
    layout->addWidget(newHeader, 0, 6);

    // 为每一行创建控件
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        // 序号标签
        QLabel* indexLabel = new QLabel(QString::number(i + 1), ui->controlValveFrame);
        indexLabel->setAlignment(Qt::AlignCenter);
        controlValveIndexLabels.append(indexLabel);
        layout->addWidget(indexLabel, i + 1, 0);

        // 步骤名称标签
        QLabel* nameLabel = new QLabel("", ui->controlValveFrame);
        nameLabel->setAlignment(Qt::AlignCenter);
        controlValveNameLabels.append(nameLabel);
        layout->addWidget(nameLabel, i + 1, 1);

        // 步标签
        QLabel* stepLabel = new QLabel("", ui->controlValveFrame);
        stepLabel->setAlignment(Qt::AlignCenter);
        controlValveStepLabels.append(stepLabel);
        layout->addWidget(stepLabel, i + 1, 2);

        // 角度标签
        QLabel* positionLabel = new QLabel("", ui->controlValveFrame);
        positionLabel->setAlignment(Qt::AlignCenter);
        controlValvePositionLabels.append(positionLabel);
        layout->addWidget(positionLabel, i + 1, 3);

        // 类型标签
        QLabel* movementLabel = new QLabel("", ui->controlValveFrame);
        movementLabel->setAlignment(Qt::AlignCenter);
        controlValveMovementLabels.append(movementLabel);
        layout->addWidget(movementLabel, i + 1, 4);

        // Old标签
        QLabel* oldLabel = new QLabel("", ui->controlValveFrame);
        oldLabel->setAlignment(Qt::AlignCenter);
        controlValveOldLabels.append(oldLabel);
        layout->addWidget(oldLabel, i + 1, 5);

        // New编辑框
        MyLineEdit* newEdit = new MyLineEdit(ui->controlValveFrame);
        newEdit->setAlignment(Qt::AlignCenter);
        newEdit->setReadOnly(true);      // 初始设为只读
        newEdit->setProperty("row", i);  // 存储行索引
        connect(newEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentControlValvePage * ITEMS_PER_PAGE + i;

            if (dataIndex < craftParams.controlValeParam.size()) {
                // 清空之前的内存
                if (this->numberInputForm != nullptr) {
                    delete this->numberInputForm;
                    this->numberInputForm = nullptr;
                }

                // 创建数字输入表单
                this->numberInputForm = new NumberInputForm(nullptr, "请输入控制阀步数", 0, 65535);

                // 存储当前编辑的行和列
                tableEditRowIndex = dataIndex;
                tableIndex = 9;  // 控制阀表格

                // 连接完成信号
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);

                // 显示表单
                this->numberInputForm->show();
            }
        });
        controlValveNewEdits.append(newEdit);
        layout->addWidget(newEdit, i + 1, 6);
    }

    // 设置布局属性
    layout->setColumnStretch(0, 1);  // 序号
    layout->setColumnStretch(1, 3);  // 步骤名称
    layout->setColumnStretch(2, 2);  // 步
    layout->setColumnStretch(3, 2);  // 角度
    layout->setColumnStretch(4, 2);  // 类型
    layout->setColumnStretch(5, 2);  // Old
    layout->setColumnStretch(6, 2);  // New
    layout->setSpacing(5);
    layout->setContentsMargins(5, 5, 5, 5);

    // 应用布局
    ui->controlValveFrame->setLayout(layout);
}

// 更新控制阀页面
void CraftParamForm::updateControlValvePage()
{
    // 计算总页数
    totalControlValvePages = (craftParams.controlValeParam.size() + ITEMS_PER_PAGE - 1) / ITEMS_PER_PAGE;

    // 确保当前页在有效范围内
    if (currentControlValvePage >= totalControlValvePages && totalControlValvePages > 0) {
        currentControlValvePage = totalControlValvePages - 1;
    }
    if (currentControlValvePage < 0) {
        currentControlValvePage = 0;
    }

    // 计算当前页的起始索引
    int startIndex = currentControlValvePage * ITEMS_PER_PAGE;

    // 更新控件显示
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        int dataIndex = startIndex + i;

        if (dataIndex < craftParams.controlValeParam.size()) {
            // 有数据，显示
            auto param = craftParams.controlValeParam[dataIndex];

            controlValveIndexLabels[i]->setText(QString::number(dataIndex + 1));

            // 使用GB18030编码解码中文字符
            QString blockNameText;
            QTextCodec* codec = QTextCodec::codecForName( "GB18030" );
            if ( codec )
            {
                blockNameText = codec->toUnicode( param->blockName );
            }
            else
            {
                blockNameText = QString::fromLocal8Bit( param->blockName );
            }
            controlValveNameLabels[i]->setText(blockNameText);

            controlValveStepLabels[i]->setText(QString::number(param->step_index));
            controlValvePositionLabels[i]->setText(QString::number(param->position));
            controlValveMovementLabels[i]->setText(QString::number(param->movement));
            controlValveOldLabels[i]->setText(QString::number(param->step));
            controlValveNewEdits[i]->setText(QString::number(param->step));
            controlValveNewEdits[i]->setStyleSheet("background-color: #ffffc8;");  // 黄色背景表示可编辑

            // 显示所有控件并设置正常样式
            controlValveIndexLabels[i]->setVisible(true);
            controlValveNameLabels[i]->setVisible(true);
            controlValveStepLabels[i]->setVisible(true);
            controlValvePositionLabels[i]->setVisible(true);
            controlValveMovementLabels[i]->setVisible(true);
            controlValveOldLabels[i]->setVisible(true);
            controlValveNewEdits[i]->setVisible(true);

            // 恢复正常样式（除了New编辑框）
            controlValveIndexLabels[i]->setStyleSheet("");
            controlValveNameLabels[i]->setStyleSheet("");
            controlValveStepLabels[i]->setStyleSheet("");
            controlValvePositionLabels[i]->setStyleSheet("");
            controlValveMovementLabels[i]->setStyleSheet("");
            controlValveOldLabels[i]->setStyleSheet("");
        } else {
            // 无数据，但保持控件可见以占据空间
            controlValveIndexLabels[i]->setText("");
            controlValveNameLabels[i]->setText("");
            controlValveStepLabels[i]->setText("");
            controlValvePositionLabels[i]->setText("");
            controlValveMovementLabels[i]->setText("");
            controlValveOldLabels[i]->setText("");
            controlValveNewEdits[i]->setText("");

            // 控件保持可见，但设置为透明
            controlValveIndexLabels[i]->setVisible(true);
            controlValveNameLabels[i]->setVisible(true);
            controlValveStepLabels[i]->setVisible(true);
            controlValvePositionLabels[i]->setVisible(true);
            controlValveMovementLabels[i]->setVisible(true);
            controlValveOldLabels[i]->setVisible(true);
            controlValveNewEdits[i]->setVisible(true);

            // 设置透明样式
            QString transparentStyle = "background-color: transparent; border: none;";
            controlValveIndexLabels[i]->setStyleSheet(transparentStyle);
            controlValveNameLabels[i]->setStyleSheet(transparentStyle);
            controlValveStepLabels[i]->setStyleSheet(transparentStyle);
            controlValvePositionLabels[i]->setStyleSheet(transparentStyle);
            controlValveMovementLabels[i]->setStyleSheet(transparentStyle);
            controlValveOldLabels[i]->setStyleSheet(transparentStyle);
            controlValveNewEdits[i]->setStyleSheet(transparentStyle);
        }
    }
}
