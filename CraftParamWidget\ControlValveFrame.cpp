#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"
#include <QGridLayout>
#include <QLabel>

// 初始化控制阀框架
void CraftParamForm::initControlValveFrame()
{
    // 清空之前的控件列表
    if (!controlValveIndexLabels.isEmpty()) {
        for (auto label : controlValveIndexLabels) {
            delete label;
        }
        controlValveIndexLabels.clear();
    }
    if (!controlValveNameLabels.isEmpty()) {
        for (auto label : controlValveNameLabels) {
            delete label;
        }
        controlValveNameLabels.clear();
    }
    if (!controlValveStepLabels.isEmpty()) {
        for (auto label : controlValveStepLabels) {
            delete label;
        }
        controlValveStepLabels.clear();
    }
    if (!controlValvePositionLabels.isEmpty()) {
        for (auto label : controlValvePositionLabels) {
            delete label;
        }
        controlValvePositionLabels.clear();
    }
    if (!controlValveMovementLabels.isEmpty()) {
        for (auto label : controlValveMovementLabels) {
            delete label;
        }
        controlValveMovementLabels.clear();
    }
    if (!controlValveOldLabels.isEmpty()) {
        for (auto label : controlValveOldLabels) {
            delete label;
        }
        controlValveOldLabels.clear();
    }
    if (!controlValveNewEdits.isEmpty()) {
        for (auto edit : controlValveNewEdits) {
            delete edit;
        }
        controlValveNewEdits.clear();
    }

    // 创建网格布局
    QGridLayout* layout = new QGridLayout();

    // 创建表头
    QLabel* headerIndex = new QLabel("序号", ui->controlValveFrame);
    headerIndex->setAlignment(Qt::AlignCenter);
    headerIndex->setStyleSheet("font-weight: bold; border: 1px solid gray;");
    layout->addWidget(headerIndex, 0, 0);

    QLabel* headerName = new QLabel("步骤名称", ui->controlValveFrame);
    headerName->setAlignment(Qt::AlignCenter);
    headerName->setStyleSheet("font-weight: bold; border: 1px solid gray;");
    layout->addWidget(headerName, 0, 1);

    QLabel* headerStep = new QLabel("步", ui->controlValveFrame);
    headerStep->setAlignment(Qt::AlignCenter);
    headerStep->setStyleSheet("font-weight: bold; border: 1px solid gray;");
    layout->addWidget(headerStep, 0, 2);

    QLabel* headerPosition = new QLabel("角度", ui->controlValveFrame);
    headerPosition->setAlignment(Qt::AlignCenter);
    headerPosition->setStyleSheet("font-weight: bold; border: 1px solid gray;");
    layout->addWidget(headerPosition, 0, 3);

    QLabel* headerMovement = new QLabel("类型", ui->controlValveFrame);
    headerMovement->setAlignment(Qt::AlignCenter);
    headerMovement->setStyleSheet("font-weight: bold; border: 1px solid gray;");
    layout->addWidget(headerMovement, 0, 4);

    QLabel* headerOld = new QLabel("Old", ui->controlValveFrame);
    headerOld->setAlignment(Qt::AlignCenter);
    headerOld->setStyleSheet("font-weight: bold; border: 1px solid gray;");
    layout->addWidget(headerOld, 0, 5);

    QLabel* headerNew = new QLabel("New", ui->controlValveFrame);
    headerNew->setAlignment(Qt::AlignCenter);
    headerNew->setStyleSheet("font-weight: bold; border: 1px solid gray;");
    layout->addWidget(headerNew, 0, 6);

    // 为每一行创建控件
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        // 序号标签
        QLabel* indexLabel = new QLabel(QString::number(i + 1), ui->controlValveFrame);
        indexLabel->setAlignment(Qt::AlignCenter);
        indexLabel->setStyleSheet("border: 1px solid gray;");
        controlValveIndexLabels.append(indexLabel);
        layout->addWidget(indexLabel, i + 1, 0);

        // 步骤名称标签
        QLabel* nameLabel = new QLabel("", ui->controlValveFrame);
        nameLabel->setAlignment(Qt::AlignCenter);
        nameLabel->setStyleSheet("border: 1px solid gray;");
        controlValveNameLabels.append(nameLabel);
        layout->addWidget(nameLabel, i + 1, 1);

        // 步标签
        QLabel* stepLabel = new QLabel("", ui->controlValveFrame);
        stepLabel->setAlignment(Qt::AlignCenter);
        stepLabel->setStyleSheet("border: 1px solid gray;");
        controlValveStepLabels.append(stepLabel);
        layout->addWidget(stepLabel, i + 1, 2);

        // 角度标签
        QLabel* positionLabel = new QLabel("", ui->controlValveFrame);
        positionLabel->setAlignment(Qt::AlignCenter);
        positionLabel->setStyleSheet("border: 1px solid gray;");
        controlValvePositionLabels.append(positionLabel);
        layout->addWidget(positionLabel, i + 1, 3);

        // 类型标签
        QLabel* movementLabel = new QLabel("", ui->controlValveFrame);
        movementLabel->setAlignment(Qt::AlignCenter);
        movementLabel->setStyleSheet("border: 1px solid gray;");
        controlValveMovementLabels.append(movementLabel);
        layout->addWidget(movementLabel, i + 1, 4);

        // Old标签
        QLabel* oldLabel = new QLabel("", ui->controlValveFrame);
        oldLabel->setAlignment(Qt::AlignCenter);
        oldLabel->setStyleSheet("border: 1px solid gray;");
        controlValveOldLabels.append(oldLabel);
        layout->addWidget(oldLabel, i + 1, 5);

        // New编辑框
        MyLineEdit* newEdit = new MyLineEdit(ui->controlValveFrame);
        newEdit->setAlignment(Qt::AlignCenter);
        newEdit->setReadOnly(true);
        newEdit->setStyleSheet("border: 1px solid gray;");
        newEdit->setProperty("row", i);
        connect(newEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentControlValvePage * ITEMS_PER_PAGE + i;
            
            if (dataIndex < craftParams.controlValeParam.size()) {
                tableIndex = 9; // 控制阀表格
                tableEditRowIndex = i;
                tableEditColIndex = 6; // New列
                
                // 创建数字输入表单
                if (numberInputForm != nullptr) {
                    delete numberInputForm;
                    numberInputForm = nullptr;
                }
                numberInputForm = new NumberInputForm(nullptr, "控制阀新值", 0, 65535);
                connect(this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished);
                numberInputForm->show();
            }
        });
        controlValveNewEdits.append(newEdit);
        layout->addWidget(newEdit, i + 1, 6);
    }

    // 设置布局属性
    layout->setColumnStretch(0, 1);  // 序号
    layout->setColumnStretch(1, 3);  // 步骤名称
    layout->setColumnStretch(2, 2);  // 步
    layout->setColumnStretch(3, 2);  // 角度
    layout->setColumnStretch(4, 2);  // 类型
    layout->setColumnStretch(5, 2);  // Old
    layout->setColumnStretch(6, 2);  // New
    layout->setSpacing(5);
    layout->setContentsMargins(5, 5, 5, 5);

    // 应用布局
    ui->controlValveFrame->setLayout(layout);
}

// 更新控制阀页面
void CraftParamForm::updateControlValvePage()
{
    // 计算总页数
    totalControlValvePages = (craftParams.controlValeParam.size() + ITEMS_PER_PAGE - 1) / ITEMS_PER_PAGE;
    
    // 确保当前页在有效范围内
    if (currentControlValvePage >= totalControlValvePages && totalControlValvePages > 0) {
        currentControlValvePage = totalControlValvePages - 1;
    }
    
    // 计算当前页的起始索引
    int startIndex = currentControlValvePage * ITEMS_PER_PAGE;
    
    // 更新控件显示
    for (int i = 0; i < ITEMS_PER_PAGE; i++) {
        int dataIndex = startIndex + i;

        if (dataIndex < craftParams.controlValeParam.size()) {
            // 有数据，显示
            auto param = craftParams.controlValeParam[dataIndex];

            controlValveIndexLabels[i]->setText(QString::number(dataIndex + 1));
            controlValveNameLabels[i]->setText(QString::fromLatin1(param->blockName));
            controlValveStepLabels[i]->setText(QString::number(param->step_index));
            controlValvePositionLabels[i]->setText(QString::number(param->position));
            controlValveMovementLabels[i]->setText(QString::number(param->movement));
            controlValveOldLabels[i]->setText(QString::number(param->step));
            controlValveNewEdits[i]->setText(QString::number(param->step));

            // 显示控件
            controlValveIndexLabels[i]->setVisible(true);
            controlValveNameLabels[i]->setVisible(true);
            controlValveStepLabels[i]->setVisible(true);
            controlValvePositionLabels[i]->setVisible(true);
            controlValveMovementLabels[i]->setVisible(true);
            controlValveOldLabels[i]->setVisible(true);
            controlValveNewEdits[i]->setVisible(true);
        } else {
            // 无数据，隐藏控件
            controlValveIndexLabels[i]->setVisible(false);
            controlValveNameLabels[i]->setVisible(false);
            controlValveStepLabels[i]->setVisible(false);
            controlValvePositionLabels[i]->setVisible(false);
            controlValveMovementLabels[i]->setVisible(false);
            controlValveOldLabels[i]->setVisible(false);
            controlValveNewEdits[i]->setVisible(false);
        }
    }
    
    // 更新翻页按钮状态
    updatePageButtonStatus();
}
