#include "CommonWidget/mylineedit.h"
#include "CommonWidget/numberinputform.h"
#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"
#include <QGridLayout>
#include <QLabel>

// 初始化提升盘电机框架
void CraftParamForm::initRaisingDialMotorFrame()
{
    // 清空之前的控件列表
    if ( !raisingDialMotorIndexLabels.isEmpty() )
    {
        for ( auto label : raisingDialMotorIndexLabels )
        {
            delete label;
        }
        raisingDialMotorIndexLabels.clear();
    }
    if ( !raisingDialMotorNameLabels.isEmpty() )
    {
        for ( auto label : raisingDialMotorNameLabels )
        {
            delete label;
        }
        raisingDialMotorNameLabels.clear();
    }
    if ( !raisingDialMotorStepLabels.isEmpty() )
    {
        for ( auto label : raisingDialMotorStepLabels )
        {
            delete label;
        }
        raisingDialMotorStepLabels.clear();
    }
    if ( !raisingDialMotorPositionLabels.isEmpty() )
    {
        for ( auto label : raisingDialMotorPositionLabels )
        {
            delete label;
        }
        raisingDialMotorPositionLabels.clear();
    }
    if ( !raisingDialMotorMovementLabels.isEmpty() )
    {
        for ( auto label : raisingDialMotorMovementLabels )
        {
            delete label;
        }
        raisingDialMotorMovementLabels.clear();
    }
    if ( !raisingDialMotorOldLabels.isEmpty() )
    {
        for ( auto label : raisingDialMotorOldLabels )
        {
            delete label;
        }
        raisingDialMotorOldLabels.clear();
    }
    if ( !raisingDialMotorNewEdits.isEmpty() )
    {
        for ( auto edit : raisingDialMotorNewEdits )
        {
            delete edit;
        }
        raisingDialMotorNewEdits.clear();
    }

    // 创建网格布局
    QGridLayout* layout = new QGridLayout( ui->raisingDialMotorFrame );

    // 创建标题行
    QLabel* indexHeader    = new QLabel( "序号", ui->raisingDialMotorFrame );
    QLabel* nameHeader     = new QLabel( "步骤名称", ui->raisingDialMotorFrame );
    QLabel* stepHeader     = new QLabel( "步", ui->raisingDialMotorFrame );
    QLabel* positionHeader = new QLabel( "角度", ui->raisingDialMotorFrame );
    QLabel* movementHeader = new QLabel( "类型", ui->raisingDialMotorFrame );
    QLabel* oldHeader      = new QLabel( "Old", ui->raisingDialMotorFrame );
    QLabel* newHeader      = new QLabel( "New", ui->raisingDialMotorFrame );

    // 设置标题样式
    QString headerStyle = "QLabel { background-color: #3498db; color: white; font-weight: bold; border-radius: 4px; padding: 4px; }";
    indexHeader->setStyleSheet( headerStyle );
    nameHeader->setStyleSheet( headerStyle );
    stepHeader->setStyleSheet( headerStyle );
    positionHeader->setStyleSheet( headerStyle );
    movementHeader->setStyleSheet( headerStyle );
    oldHeader->setStyleSheet( headerStyle );
    newHeader->setStyleSheet( headerStyle );
    indexHeader->setAlignment( Qt::AlignCenter );
    nameHeader->setAlignment( Qt::AlignCenter );
    stepHeader->setAlignment( Qt::AlignCenter );
    positionHeader->setAlignment( Qt::AlignCenter );
    movementHeader->setAlignment( Qt::AlignCenter );
    oldHeader->setAlignment( Qt::AlignCenter );
    newHeader->setAlignment( Qt::AlignCenter );

    // 添加标题到布局
    layout->addWidget( indexHeader, 0, 0 );
    layout->addWidget( nameHeader, 0, 1 );
    layout->addWidget( stepHeader, 0, 2 );
    layout->addWidget( positionHeader, 0, 3 );
    layout->addWidget( movementHeader, 0, 4 );
    layout->addWidget( oldHeader, 0, 5 );
    layout->addWidget( newHeader, 0, 6 );

    // 为每一行创建控件
    for ( int i = 0; i < ITEMS_PER_PAGE; i++ )
    {
        // 序号标签
        QLabel* indexLabel = new QLabel( QString::number( i + 1 ), ui->raisingDialMotorFrame );
        indexLabel->setAlignment( Qt::AlignCenter );
        raisingDialMotorIndexLabels.append( indexLabel );
        layout->addWidget( indexLabel, i + 1, 0 );

        // 步骤名称标签
        QLabel* nameLabel = new QLabel( "", ui->raisingDialMotorFrame );
        nameLabel->setAlignment( Qt::AlignCenter );
        raisingDialMotorNameLabels.append( nameLabel );
        layout->addWidget( nameLabel, i + 1, 1 );

        // 步标签
        QLabel* stepLabel = new QLabel( "", ui->raisingDialMotorFrame );
        stepLabel->setAlignment( Qt::AlignCenter );
        raisingDialMotorStepLabels.append( stepLabel );
        layout->addWidget( stepLabel, i + 1, 2 );

        // 角度标签
        QLabel* positionLabel = new QLabel( "", ui->raisingDialMotorFrame );
        positionLabel->setAlignment( Qt::AlignCenter );
        raisingDialMotorPositionLabels.append( positionLabel );
        layout->addWidget( positionLabel, i + 1, 3 );

        // 类型标签
        QLabel* movementLabel = new QLabel( "", ui->raisingDialMotorFrame );
        movementLabel->setAlignment( Qt::AlignCenter );
        raisingDialMotorMovementLabels.append( movementLabel );
        layout->addWidget( movementLabel, i + 1, 4 );

        // Old标签
        QLabel* oldLabel = new QLabel( "", ui->raisingDialMotorFrame );
        oldLabel->setAlignment( Qt::AlignCenter );
        raisingDialMotorOldLabels.append( oldLabel );
        layout->addWidget( oldLabel, i + 1, 5 );

        // New编辑框
        MyLineEdit* newEdit = new MyLineEdit( ui->raisingDialMotorFrame );
        newEdit->setAlignment( Qt::AlignCenter );
        newEdit->setReadOnly( true );      // 初始设为只读
        newEdit->setProperty( "row", i );  // 存储行索引
        connect( newEdit, &MyLineEdit::mouseRelease, this, [=]() {
            int dataIndex = currentRaisingDialMotorPage * ITEMS_PER_PAGE + i;

            if ( dataIndex < craftParams.raisingDialMotorParam.size() )
            {
                // 清空之前的内存
                if ( this->numberInputForm != nullptr )
                {
                    delete this->numberInputForm;
                    this->numberInputForm = nullptr;
                }

                // 创建数字输入表单
                this->numberInputForm = new NumberInputForm( nullptr, "请输入提升盘电机步数", 0, 65535 );

                // 存储当前编辑的行和列
                tableEditRowIndex = dataIndex;
                tableIndex        = 10;  // 提升盘电机表格

                // 连接完成信号
                connect( this->numberInputForm, &NumberInputForm::InputFinished, this, &CraftParamForm::onNumberInputFormFinished );

                // 显示表单
                this->numberInputForm->show();
            }
        } );
        raisingDialMotorNewEdits.append( newEdit );
        layout->addWidget( newEdit, i + 1, 6 );
    }

    // 设置布局属性
    layout->setColumnStretch( 0, 1 );  // 序号
    layout->setColumnStretch( 1, 3 );  // 步骤名称
    layout->setColumnStretch( 2, 2 );  // 步
    layout->setColumnStretch( 3, 2 );  // 角度
    layout->setColumnStretch( 4, 2 );  // 类型
    layout->setColumnStretch( 5, 2 );  // Old
    layout->setColumnStretch( 6, 2 );  // New
    layout->setSpacing( 5 );
    layout->setContentsMargins( 5, 5, 5, 5 );

    // 应用布局
    ui->raisingDialMotorFrame->setLayout( layout );
}

// 更新提升盘电机页面
void CraftParamForm::updateRaisingDialMotorPage()
{
    // 计算总页数
    totalRaisingDialMotorPages = ( craftParams.raisingDialMotorParam.size() + ITEMS_PER_PAGE - 1 ) / ITEMS_PER_PAGE;

    // 确保当前页在有效范围内
    if ( currentRaisingDialMotorPage >= totalRaisingDialMotorPages && totalRaisingDialMotorPages > 0 )
    {
        currentRaisingDialMotorPage = totalRaisingDialMotorPages - 1;
    }
    if ( currentRaisingDialMotorPage < 0 )
    {
        currentRaisingDialMotorPage = 0;
    }

    // 计算当前页的起始索引
    int startIndex = currentRaisingDialMotorPage * ITEMS_PER_PAGE;

    // 更新控件显示
    for ( int i = 0; i < ITEMS_PER_PAGE; i++ )
    {
        int dataIndex = startIndex + i;

        if ( dataIndex < craftParams.raisingDialMotorParam.size() )
        {
            // 有数据，显示
            auto param = craftParams.raisingDialMotorParam[ dataIndex ];

            raisingDialMotorIndexLabels[ i ]->setText( QString::number( dataIndex + 1 ) );

            // 使用GB18030编码解码中文字符
            QString     blockNameText;
            QTextCodec* codec = QTextCodec::codecForName( "GB18030" );
            if ( codec )
            {
                blockNameText = codec->toUnicode( param->blockName );
            }
            else
            {
                blockNameText = QString::fromLocal8Bit( param->blockName );
            }
            raisingDialMotorNameLabels[ i ]->setText( blockNameText );

            raisingDialMotorStepLabels[ i ]->setText( QString::number( param->step_index ) );
            raisingDialMotorPositionLabels[ i ]->setText( QString::number( param->position ) );
            raisingDialMotorMovementLabels[ i ]->setText( QString( param->movementCode ) );
            raisingDialMotorOldLabels[ i ]->setText( QString::number( param->motorSteps ) );
            raisingDialMotorNewEdits[ i ]->setText( QString::number( param->motorSteps ) );
            raisingDialMotorNewEdits[ i ]->setStyleSheet( "background-color: #ffffc8;" );  // 黄色背景表示可编辑

            // 显示所有控件并设置正常样式
            raisingDialMotorIndexLabels[ i ]->setVisible( true );
            raisingDialMotorNameLabels[ i ]->setVisible( true );
            raisingDialMotorStepLabels[ i ]->setVisible( true );
            raisingDialMotorPositionLabels[ i ]->setVisible( true );
            raisingDialMotorMovementLabels[ i ]->setVisible( true );
            raisingDialMotorOldLabels[ i ]->setVisible( true );
            raisingDialMotorNewEdits[ i ]->setVisible( true );

            // 恢复正常样式（除了New编辑框）
            raisingDialMotorIndexLabels[ i ]->setStyleSheet( "" );
            raisingDialMotorNameLabels[ i ]->setStyleSheet( "" );
            raisingDialMotorStepLabels[ i ]->setStyleSheet( "" );
            raisingDialMotorPositionLabels[ i ]->setStyleSheet( "" );
            raisingDialMotorMovementLabels[ i ]->setStyleSheet( "" );
            raisingDialMotorOldLabels[ i ]->setStyleSheet( "" );
        }
        else
        {
            // 无数据，但保持控件可见以占据空间
            raisingDialMotorIndexLabels[ i ]->setText( "" );
            raisingDialMotorNameLabels[ i ]->setText( "" );
            raisingDialMotorStepLabels[ i ]->setText( "" );
            raisingDialMotorPositionLabels[ i ]->setText( "" );
            raisingDialMotorMovementLabels[ i ]->setText( "" );
            raisingDialMotorOldLabels[ i ]->setText( "" );
            raisingDialMotorNewEdits[ i ]->setText( "" );

            // 控件保持可见，但设置为透明
            raisingDialMotorIndexLabels[ i ]->setVisible( true );
            raisingDialMotorNameLabels[ i ]->setVisible( true );
            raisingDialMotorStepLabels[ i ]->setVisible( true );
            raisingDialMotorPositionLabels[ i ]->setVisible( true );
            raisingDialMotorMovementLabels[ i ]->setVisible( true );
            raisingDialMotorOldLabels[ i ]->setVisible( true );
            raisingDialMotorNewEdits[ i ]->setVisible( true );

            // 设置透明样式
            QString transparentStyle = "background-color: transparent; border: none;";
            raisingDialMotorIndexLabels[ i ]->setStyleSheet( transparentStyle );
            raisingDialMotorNameLabels[ i ]->setStyleSheet( transparentStyle );
            raisingDialMotorStepLabels[ i ]->setStyleSheet( transparentStyle );
            raisingDialMotorPositionLabels[ i ]->setStyleSheet( transparentStyle );
            raisingDialMotorMovementLabels[ i ]->setStyleSheet( transparentStyle );
            raisingDialMotorOldLabels[ i ]->setStyleSheet( transparentStyle );
            raisingDialMotorNewEdits[ i ]->setStyleSheet( transparentStyle );
        }
    }
}
