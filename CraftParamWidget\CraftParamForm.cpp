#include "CraftParamForm.h"
#include "ui_CraftParamForm.h"

CraftParamForm::CraftParamForm( QWidget* parent, MainWidgetData* mainData, Communication* comm ) : QWidget( parent ), ui( new Ui::CraftParamForm ), mainData( mainData ), comm( comm )
{
    ui->setupUi( this );

    /* 返回主菜单键连接  */
    connect( ui->CraftFormHome_btn, SIGNAL( clicked() ), this, SLOT( onHomeBtnClicked() ) );

    menuBtnGroup = new QButtonGroup( this );
    menuBtnGroup->addButton( ui->Craft_1_btn, 1 );
    menuBtnGroup->addButton( ui->Craft_2_btn, 2 );
    menuBtnGroup->addButton( ui->Craft_3_btn, 3 );
    menuBtnGroup->addButton( ui->Craft_4_btn, 4 );
    menuBtnGroup->addButton( ui->Craft_5_btn, 5 );
    menuBtnGroup->addButton( ui->Craft_6_btn, 6 );
    menuBtnGroup->addButton( ui->Craft_7_btn, 7 );
    menuBtnGroup->addButton( ui->Craft_8_btn, 8 );
    menuBtnGroup->addButton( ui->Craft_9_btn, 9 );
    menuBtnGroup->addButton( ui->Craft_10_btn, 10 );
    menuBtnGroup->addButton( ui->Craft_11_btn, 11 );
    menuBtnGroup->addButton( ui->Craft_12_btn, 12 );
    menuBtnGroup->addButton( ui->Craft_13_btn, 13 );
    menuBtnGroup->addButton( ui->Craft_14_btn, 14 );
    menuBtnGroup->addButton( ui->Craft_15_btn, 15 );
    menuBtnGroup->addButton( ui->Craft_16_btn, 16 );
    menuBtnGroup->addButton( ui->Craft_17_btn, 17 );
    menuBtnGroup->addButton( ui->Craft_18_btn, 18 );

    connect( menuBtnGroup, SIGNAL( buttonClicked( int ) ), this, SLOT( onMenuBtnGroupClicked( int ) ) );

    ui->stackedWidget->setCurrentIndex( 0 );

    //    initCraftPage();
}

CraftParamForm::~CraftParamForm()
{
    delete ui;
}

// 事件过滤器
bool CraftParamForm::eventFilter( QObject* obj, QEvent* event )
{
    // 默认实现，可以在需要时扩展
    return QWidget::eventFilter( obj, event );
}

/* 测试界面返回 主菜单键按下槽函数 */
void CraftParamForm::onHomeBtnClicked()
{
    // 导入导出页回到工艺参数页
    if ( ui->stackedWidget->currentIndex() > 0 )
    {
        ui->stackedWidget->setCurrentIndex( 0 );
        ui->Craft_Tiltle_label->setText( "工艺参数" );
    }
    else
    {
        this->close();
        emit CraftParamFormToMainWinToShowSignal();
    }
}

void CraftParamForm::onMenuBtnGroupClicked( int id )
{
    switch ( id )
    {
        case 1:
            ui->Craft_Tiltle_label->setText( "速度" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 0 );
            initCraftPage();
            break;
        case 2:
            ui->Craft_Tiltle_label->setText( "密度" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 1 );
            initCraftPage();
            break;
        case 3:
            ui->Craft_Tiltle_label->setText( "橡筋1" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 2 );
            initCraftPage();
            break;
        case 4:
            ui->Craft_Tiltle_label->setText( "橡筋2" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 3 );
            initCraftPage();
            break;
        case 5:
            ui->Craft_Tiltle_label->setText( "圆盘剪刀" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 4 );
            initCraftPage();
            break;
        case 6:
            ui->Craft_Tiltle_label->setText( "生克" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 5 );
            initCraftPage();
            break;
        case 7:
            ui->Craft_Tiltle_label->setText( "生克罩摆动" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 6 );
            initCraftPage();
            break;
        case 8:
            ui->Craft_Tiltle_label->setText( "节约" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 7 );
            initCraftPage();
            break;
        case 9:
            ui->Craft_Tiltle_label->setText( "控制阀" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 8 );
            initCraftPage();
            break;
        case 10:
            ui->Craft_Tiltle_label->setText( "机头高度" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 9 );
            initCraftPage();
            break;
        case 11:
            ui->Craft_Tiltle_label->setText( "头跟结束回转距离" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 10 );
            initCraftPage();
            break;
        case 12:
            ui->Craft_Tiltle_label->setText( "橡筋剪刀角度" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 11 );
            initCraftPage();
            break;
        case 13:
            ui->Craft_Tiltle_label->setText( "KTF张力" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 12 );
            initCraftPage();
            break;
        case 14:
            ui->Craft_Tiltle_label->setText( "加油件数" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 13 );
            initCraftPage();
            break;
        case 15:
            ui->Craft_Tiltle_label->setText( "梭子状态" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 14 );
            initCraftPage();
            break;
        case 16:
            ui->Craft_Tiltle_label->setText( "程序梭子命令" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 15 );
            initCraftPage();
            break;
        case 17:
            ui->Craft_Tiltle_label->setText( "程序命令" );
            ui->stackedWidget->setCurrentIndex( 1 );
            ui->stackedWidget_small->setCurrentIndex( 16 );
            initCraftPage();
            break;
        case 18:
            ui->Craft_Tiltle_label->setText( "导入导出" );
            ui->stackedWidget->setCurrentIndex( 2 );
            initCraftPage();
            break;
    }
}
