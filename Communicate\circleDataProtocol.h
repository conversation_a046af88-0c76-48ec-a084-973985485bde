#ifndef CIRCLEDATAPROTOCOL_H
#define CIRCLEDATAPROTOCOL_H

#include <QTypeInfo>

#pragma pack( 1 )
// 一圈基本控制数据（上位机发送到下位机）
#define CIRCLE_DATA_FU_SUO_NUM 6
#define CIRCLE_DATA_NEEDLE_DATA_MAX 200
#define CIRCLE_DATA_NEEDLE_TEMP_NUM 10
#define CIRCLE_DATA_MAIN_FINGER_NUM 40
#define CIRCLE_DATA_VALVE_ACTION_MAX 80
#define CIRCLE_DATA_MOTOR_NUM 9
#define CIRCLE_DATA_YOYO_YARN_NUM 8
typedef struct
{
    // 1. 基本信息
    quint16 circle_id;    // 圈号
    quint8  circle_type;  // 圈类型
    quint8  direction;    // 编织方向
    quint8  fu_suo[ CIRCLE_DATA_FU_SUO_NUM ];    // 副梭手指数据

    // 2. 主电机控制
    struct
    {
        qint16 start_pos;  // 起点
        qint16 end_pos;    // 终点
        qint16 speed;      // 目标速度
        quint16 delay;     // 延时(单位ms)，指的是执行多长时间后恢复原速度
    } main_motor;

    // 3. 选针动作
    struct
    {
        quint16 selector;  // 9个选针器
        quint8  nozzle;    // 沙嘴信息
    } needle_data[ CIRCLE_DATA_NEEDLE_DATA_MAX ];

    // 4. 选针器临时变更
    quint8 needle_change_count;  // 数量
    struct
    {
        quint16 angle;     // 哪个角度变更
        quint16 selector;  // 哪些选针器变更，每1位表示一个选针器，1表示需要变更，0表示不需要变更
        quint16 value;     // 变更为什么情况，若selector为0，则value值每1位表示选针器的变更情况，1为全上0为全下。若selector不为0，则value值每1位表示该选针器的刀片的状态，1为上0为下。
    } needle_temp_data[ CIRCLE_DATA_NEEDLE_TEMP_NUM ];

    // 5. 主梭
    struct
    {
        quint8  id;              // 主梭ID  最高位表示主梭类型
        quint16 work_on_angle;   // 动作开位置 最高位表示工作状态，1 执行
        quint16 work_off_angle;  // 动作关位置 最高位表示工作状态，1 执行
    } main_finger[ CIRCLE_DATA_MAIN_FINGER_NUM ];

    // 6. 气阀动作点
    quint8 valve_count;  // 数量
    struct
    {
        quint16 valve_id;     // 气阀ID
        quint16 angle;        // 动作角度
        quint8  valve_state;  // 气阀状态
        quint16 delay;        // 延时(单位ms)，指的是执行多长时间后恢复原动作
    } valve_actions[ CIRCLE_DATA_VALVE_ACTION_MAX ];

    // 7. 9个马达的数据   顺序依次为：剪刀盘 右棱角 左棱角 针筒密度 橡筋 机头升降 织袜风门 生克电机 沉降电机
    struct
    {
        quint16 position;  // 角度
        quint16 value;     // 运行速度
    } motor_data[ CIRCLE_DATA_MOTOR_NUM ];

    // 8. 纱线控制
    quint8 yoyo_yarn_count;
    struct
    {
        quint8  motor;
        char    action;
        quint8  variation;
        quint16 position;
    } yoyo_yarn[ CIRCLE_DATA_YOYO_YARN_NUM ];

    // 9. 关键控制标志
    union
    {
        quint32 all_flags;
        struct
        {            // 第一组 - 基本控制标志
            quint32 need_stop : 1;         // 需要停(F4)
            quint32 need_slow : 1;         // 需要减速
            quint32 check_yarn : 1;        // 检查纱
            quint32 check_needle : 1;      // 检查选针
            quint32 is_critical : 1;       // 关键
            quint32 is_running : 1;        // 启动
            quint32 is_reset : 1;          // 复位
            quint32 stop_when_finish : 1;  // 结束时停
            
            // 第二组 - 袜子编织流程标志
            quint32 bz_hou_gen : 1;        // 标准后跟
            quint32 tou_gen_end : 1;       // 头跟结束
            quint32 js_sf_ban_gen : 1;     // 结束缩放后半跟
            quint32 chuan_wa_part1 : 1;    // 船袜第一部分
            quint32 chuan_wa_part2 : 1;    // 船袜第二部分
            quint32 chuan_wa_hou_gen : 1;  // 船袜后跟结果
            quint32 ks_xiao_gen : 1;       // 开始小跟
            quint32 ks_xiao_fan_gen : 1;   // 开始小反跟
            
            // 第三组 - 高级编织流程标志
            quint32 ks_fan_gen : 1;        // 开始反跟
            quint32 fan_gen_end : 1;       // 反跟结束
            quint32 ks_da_fan_gen : 1;     // 开始大反跟
            quint32 hou_gen_hs_jx : 1;     // 后跟换梭剪线往复行程调整
            quint32 da_fan_gen_end : 1;    // 大反跟结束
            quint32 ks_da_gen : 1;         // 开始大跟
            quint32 da_gen_end : 1;        // 大跟结束
            quint32 ks_feng_tou : 1;       // 开始缝头过程

            quint32 reserved : 8;          // 保留
        } bits;
    } control_flags;

} CircleData;

#pragma pack()
#endif  // CIRCLEDATAPROTOCOL_H
