#ifndef SOKBUSINESS_H
#define SOKBUSINESS_H

#include "Common/CommOperations.h"
#include "Common/defines.h"
#include "Communicate/circleDataProtocol.h"
#include "CoreBusiness/SokParser.h"
#include "CraftParamWidget/CraftParamForm.h"
#include "WorkWidget/WorkForm.h"
#include <QMap>
#include <QMutex>
#include <QObject>
#include <QSharedPointer>
#include <QVector>

class SokBusiness : public QObject
{
    Q_OBJECT
public:
    explicit SokBusiness( MainWidgetData* mainData, CommOperations* commOps, QObject* parent = nullptr );
    ~SokBusiness();

#pragma pack( 1 )
    // 模块数据结构体
    struct ModuleDataItem
    {
        QString moduleName;  // 模块名称
        int     startStep;   // 起始步骤
        int     endStep;     // 结束步骤
    };

    struct SlzDataItem
    {
        QString data[2];
        quint8  drum;
        quint16 position;
        quint8  currentLine;  // 当前使用的行数，0表示data[0]，1表示data[1]
    };

    struct GussetDataItem
    {
        QString data[2];
        quint8  drum;
        quint16 position;
        quint16 quantity;
        quint8  piority;
        quint8  currentLine;  // 当前使用的行数，0表示data[0]，1表示data[1]
    };

    struct EconomizationItem
    {
        quint16 step_index;
        quint8  economizations[ 8 ];
        quint8  forStep;
    };

#pragma pack()

    // 设置和获取当前sok文件
    void    setSokFile( const QString& sokFilePath );
    QString getSokFile() const;

    // 设置和获取当前工艺文件
    void    setCraftFile( const QString& craftFilePath );
    QString getCraftFile() const;

    // 设置和获取当前尺寸
    void setCurrentSize( int size );
    int  getCurrentSize() const;

    // 解析sok文件
    bool readAndParseSokFile();
    // 解析工艺文件
    bool readAndParseCftFile();
    // 根据工艺文件计算电机参数
    void calculateMotorParamsByCft();
    // 根据sok文件计算电机参数
    void caculateMotorParmBySok();

    // 处理步骤数据，生成每圈的业务数据
    bool processStepData();

    // 获取最大步骤索引
    int getMaxStepIndex() const;

    bool sendNextStepData();

    // 获取指定步骤的圈数据
    QSharedPointer< CircleData > getStepData( int stepIndex );
    // 获取当前步骤的圈数据
    QSharedPointer< CircleData > getCurrentStepData();

    // 获取当前步骤的SpecialFunctionStr
    QString getSpecialFunctionStr();

    // 获取模块信息
    ModuleDataItem getModuleInfo();

    // 获取速度设置
    int getSpeedSet();

    // 获取密度电机数据
    int getDensityMotorSpeed( int motorIndex );

    // 获取当前运行的步骤索引
    int getCurrentRunningStepIdx() const;
    // 获取当前经济化计数
    int getCurrentEconomization() const;
    // 获取经济化设置值
    int getEconomizationSetting() const;
    // 计算并检查下一圈
    void getAndCheckNextCircle();
    // 重置当前运行步骤索引
    void resetCurrentRunningStepIdx();
    // 将当前步骤索引减2
    void decrementCurrentRunningStepIdx();

    // 设置关键控制标志
    bool setControlFlag(quint16 flagBit, bool value);

    // 获取单件停车状态
    bool getSingleStop() const;
    // 设置单件停车
    void setSingleStop(bool value);
    // 设置步段停车
    void setF4Stop(bool value);
    // 获取步段停车状态
    bool getF4Stop() const;
    // 设置低速锁定
    void setLowSpeedLock(bool value);
    // 获取低速锁定状态
    bool getLowSpeedLock() const;

signals:
    // 文件解析完成信号
    void sokFileParseCompleted( bool success );
    void craftFileParseCompleted( bool success );
    void stepDataProcessCompleted( bool success );
    // 新增信号 - 所有步骤数据发送完成
    void AllStepDataSendSuccess();

private:
    QString currentSokFile;
    QString currentCraftFile;
    int     currentSize = 0;

    SokParser*                               sokParser;
    QSharedPointer< SokParser::SokFileData > sokFileData;

    // 步骤数据映射
    QMap< int, QSharedPointer< CircleData > > stepDataMap;
    // 经济化向量
    QVector< EconomizationItem > econoVector;
    // 工艺相关
    QSharedPointer< CraftParamForm::CraftParam > craftParams;

    // 最大步骤索引
    int maxStepIndex = 0;
    // 当前运行步骤索引
    qint16 currentRunningStepIdx = 0;
    // 用于执行时，计算经济化
    quint16 currentEconomization = 0;
    int     economizationSetting = 0;  // 经济化设置值

    // 单件停车
    bool isSingleStop = false;
    // F4步段停车
    bool isF4Stop = false;
    // 低速锁定
    bool isLowSpeedLock = false;

    // 互斥锁
    QMutex mutex;

    // 主数据引用
    MainWidgetData* mainData;

    // 通信操作对象
    CommOperations* m_commOperations = nullptr;

    // 计算下一圈
    void calculateNextCircle();

    // 将字符串数据补足8位的通用函数
    void padDataTo8Bits(QString& data);

    // 验证SokFileData数据的合法性
    bool validateSokFileData(const SokParser::SokFileData* data);
};

#endif  // SOKBUSINESS_H
