<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>WorkForm</class>
 <widget class="QWidget" name="WorkForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QStackedWidget" name="stackedWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1031</width>
     <height>601</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: #F5F7FA;</string>
   </property>
   <property name="currentIndex">
    <number>0</number>
   </property>
   <widget class="QWidget" name="page">
    <widget class="QPushButton" name="pbtn_Tab">
     <property name="geometry">
      <rect>
       <x>920</x>
       <y>80</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #3498DB;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #2980B9;
}
QPushButton:pressed { 
    background-color: #1A5276;
}</string>
     </property>
     <property name="text">
      <string>状态
信息</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_Special">
     <property name="geometry">
      <rect>
       <x>920</x>
       <y>160</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #E74C3C;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #C0392B;
}
QPushButton:pressed { 
    background-color: #922B21;
}</string>
     </property>
     <property name="text">
      <string>报警
信息</string>
     </property>
    </widget>
    <widget class="QLabel" name="label">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>70</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>文件</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_sokFile">
     <property name="geometry">
      <rect>
       <x>240</x>
       <y>70</y>
       <width>231</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_4">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>110</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>工艺</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_size">
     <property name="geometry">
      <rect>
       <x>240</x>
       <y>150</y>
       <width>231</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_craftFile">
     <property name="geometry">
      <rect>
       <x>240</x>
       <y>110</y>
       <width>231</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_3">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>150</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>尺寸</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_2">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>20</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>机器名称:</string>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_machineName">
     <property name="geometry">
      <rect>
       <x>240</x>
       <y>20</y>
       <width>181</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_curStepIdx">
     <property name="geometry">
      <rect>
       <x>240</x>
       <y>190</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_maxStepIdx">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>190</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_economCur">
     <property name="geometry">
      <rect>
       <x>650</x>
       <y>190</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_economSet">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>190</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_9">
     <property name="geometry">
      <rect>
       <x>530</x>
       <y>190</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>节约</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_8">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>190</y>
       <width>67</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>步段</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_14">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>320</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>电眼状态：</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_module">
     <property name="geometry">
      <rect>
       <x>240</x>
       <y>270</y>
       <width>461</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_5">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>270</y>
       <width>67</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>域</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_module_maxStep">
     <property name="geometry">
      <rect>
       <x>800</x>
       <y>270</y>
       <width>81</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_module_minStep">
     <property name="geometry">
      <rect>
       <x>710</x>
       <y>270</y>
       <width>81</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_link">
     <property name="geometry">
      <rect>
       <x>650</x>
       <y>110</y>
       <width>231</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="label_27">
     <property name="geometry">
      <rect>
       <x>530</x>
       <y>110</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>连织</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_12">
     <property name="geometry">
      <rect>
       <x>530</x>
       <y>230</y>
       <width>67</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>针/角</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_abNeedle">
     <property name="geometry">
      <rect>
       <x>650</x>
       <y>230</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_abAngle">
     <property name="geometry">
      <rect>
       <x>770</x>
       <y>230</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_set_speed">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>230</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_6">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>230</y>
       <width>67</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>速度</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_current_speed">
     <property name="geometry">
      <rect>
       <x>240</x>
       <y>230</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_28">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>360</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>零位信号：</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_29">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>400</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>脉冲数量：</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_30">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>440</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>针筒方向：</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_31">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>320</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>设定产量：</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_32">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>360</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>当前产量：</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_33">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>400</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>上件用时：</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_34">
     <property name="geometry">
      <rect>
       <x>380</x>
       <y>440</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>本件用时：</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_35">
     <property name="geometry">
      <rect>
       <x>640</x>
       <y>320</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>缝头控制：</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_36">
     <property name="geometry">
      <rect>
       <x>640</x>
       <y>360</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>缝头状态：</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_37">
     <property name="geometry">
      <rect>
       <x>640</x>
       <y>400</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>机头升降：</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_38">
     <property name="geometry">
      <rect>
       <x>640</x>
       <y>440</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>断电续织：</string>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_eyeStatus">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>320</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_zeroSignal">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>360</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_pulseNum">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>400</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_zhentDir">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>440</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_fengtCtrl">
     <property name="geometry">
      <rect>
       <x>760</x>
       <y>320</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_fentStatus">
     <property name="geometry">
      <rect>
       <x>760</x>
       <y>360</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_headUpDown">
     <property name="geometry">
      <rect>
       <x>760</x>
       <y>400</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_lostPowerContinue">
     <property name="geometry">
      <rect>
       <x>760</x>
       <y>440</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_hintText">
     <property name="geometry">
      <rect>
       <x>110</x>
       <y>500</y>
       <width>771</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
	border: none;
	color: white;
	background-color: #2C3E50;
	border-radius: 5px;
}</string>
     </property>
     <property name="text">
      <string>TextLabel</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_7">
     <property name="geometry">
      <rect>
       <x>530</x>
       <y>70</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>机型</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_machineModel">
     <property name="geometry">
      <rect>
       <x>650</x>
       <y>70</y>
       <width>231</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_39">
     <property name="geometry">
      <rect>
       <x>530</x>
       <y>150</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {     
	color: #2980B9;
	font-weight: bold;
}</string>
     </property>
     <property name="text">
      <string>密度(TCS)</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_denstity1">
     <property name="geometry">
      <rect>
       <x>650</x>
       <y>150</y>
       <width>71</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_denstity2">
     <property name="geometry">
      <rect>
       <x>730</x>
       <y>150</y>
       <width>71</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_denstity3">
     <property name="geometry">
      <rect>
       <x>810</x>
       <y>150</y>
       <width>71</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_Pattern">
     <property name="geometry">
      <rect>
       <x>920</x>
       <y>0</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #9B59B6;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #8E44AD;
}
QPushButton:pressed { 
    background-color: #6C3483;
}</string>
     </property>
     <property name="text">
      <string>花型
预览</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_Reset">
     <property name="geometry">
      <rect>
       <x>920</x>
       <y>240</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #9B59B6;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #8E44AD;
}
QPushButton:pressed { 
    background-color: #6C3483;
}</string>
     </property>
     <property name="text">
      <string>机器
复位</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_F9">
     <property name="geometry">
      <rect>
       <x>920</x>
       <y>320</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #3498DB;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #2980B9;
}
QPushButton:pressed { 
    background-color: #1A5276;
}</string>
     </property>
     <property name="text">
      <string>照明
On</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_F0">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>0</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #3498DB;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #2980B9;
}
QPushButton:pressed { 
    background-color: #1A5276;
}</string>
     </property>
     <property name="text">
      <string>程序
复位</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_F1">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>80</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #3498DB;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #2980B9;
}
QPushButton:pressed { 
    background-color: #1A5276;
}</string>
     </property>
     <property name="text">
      <string>链条
锁定</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_F2">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>160</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #3498DB;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #2980B9;
}
QPushButton:pressed { 
    background-color: #1A5276;
}</string>
     </property>
     <property name="text">
      <string>节约
模式</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_F3">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>240</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #27AE60;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #2980B9;
}
QPushButton:pressed { 
    background-color: #1A5276;
}</string>
     </property>
     <property name="text">
      <string>单件
停车</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_F4">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>320</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #27AE60;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #2980B9;
}
QPushButton:pressed { 
    background-color: #1A5276;
}</string>
     </property>
     <property name="text">
      <string>步段
停车</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_F5">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>400</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #27AE60;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #2980B9;
}
QPushButton:pressed { 
    background-color: #1A5276;
}</string>
     </property>
     <property name="text">
      <string>低速
锁定</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_F6">
     <property name="geometry">
      <rect>
       <x>920</x>
       <y>400</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #3498DB;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #2980B9;
}
QPushButton:pressed { 
    background-color: #1A5276;
}</string>
     </property>
     <property name="text">
      <string>限速
350</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_R">
     <property name="geometry">
      <rect>
       <x>920</x>
       <y>480</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #E74C3C;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #C0392B;
}
QPushButton:pressed { 
    background-color: #922B21;
}</string>
     </property>
     <property name="text">
      <string>返回
主界面</string>
     </property>
    </widget>
    <widget class="QWidget" name="widget" native="true">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>560</y>
       <width>1024</width>
       <height>41</height>
      </rect>
     </property>
     <widget class="MyLineEdit" name="le_currentTime">
      <property name="geometry">
       <rect>
        <x>730</x>
        <y>5</y>
        <width>111</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_currentDate">
      <property name="geometry">
       <rect>
        <x>850</x>
        <y>5</y>
        <width>171</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
     <widget class="MyLineEdit" name="le_netIp">
      <property name="geometry">
       <rect>
        <x>530</x>
        <y>5</y>
        <width>191</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
      </property>
      <property name="text">
       <string>0.0.0.0</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </widget>
    <widget class="QPushButton" name="pbtn_Space">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>480</y>
       <width>81</width>
       <height>71</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:10px;
    background-color: #27AE60;
    color: white;
    border: none;
}
QPushButton:hover {
    background-color: #229954;
}
QPushButton:pressed { 
    background-color: #1E8449;
}</string>
     </property>
     <property name="text">
      <string>运行
按钮</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_basNumSet">
     <property name="geometry">
      <rect>
       <x>510</x>
       <y>320</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_basNumCur">
     <property name="geometry">
      <rect>
       <x>510</x>
       <y>360</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_timeLastProd">
     <property name="geometry">
      <rect>
       <x>510</x>
       <y>400</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>00:00</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_timeThisProd">
     <property name="geometry">
      <rect>
       <x>510</x>
       <y>440</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLineEdit {
	background-color: white;
   border: 1px solid #BDC3C7;
   border-radius: 4px;
   padding: 2px;
}</string>
     </property>
     <property name="text">
      <string>00:00</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_2">
    <widget class="QTabWidget" name="tabWidget_Warn">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>50</y>
       <width>1021</width>
       <height>551</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTabBar::tab:selected {
	background-color: rgb(255, 142, 61);
    color: #fff;
}
QWidget{
	background-color: #929697;
}</string>
     </property>
     <property name="currentIndex">
      <number>10</number>
     </property>
     <widget class="QWidget" name="tabWarn1">
      <attribute name="title">
       <string>袜机电磁阀</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tabWarn2">
      <attribute name="title">
       <string>三角</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tabWarn3">
      <attribute name="title">
       <string>梭子</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tabWarn4">
      <attribute name="title">
       <string>选针输出</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tabWarn5">
      <attribute name="title">
       <string>输入</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tabWarn6">
      <attribute name="title">
       <string>步进电机</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tabWarn7">
      <attribute name="title">
       <string>伺服及主编码器</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tabWarn8">
      <attribute name="title">
       <string>通信</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tabWarn9">
      <attribute name="title">
       <string>缝头</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tabWarn10">
      <attribute name="title">
       <string>电眼</string>
      </attribute>
     </widget>
     <widget class="QWidget" name="tabWarn11">
      <attribute name="title">
       <string>软件应用</string>
      </attribute>
     </widget>
    </widget>
    <widget class="ClickableLabel" name="label_mainTitle">
     <property name="geometry">
      <rect>
       <x>281</x>
       <y>5</y>
       <width>431</width>
       <height>40</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Ubuntu Condensed</family>
       <pointsize>12</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>当前报警</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QPushButton" name="WorkFormHome_pbtn_back">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>51</width>
       <height>51</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/back.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_3">
    <widget class="QPushButton" name="WorkFormHome_pbtn_back_2">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>51</width>
       <height>51</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
    border-radius:5px;					    /* 按钮边框的圆角设置 */
    /* 按钮背景图标设置 */
	border-image: url(:/back.png);
	background-color: rgb(255, 255, 255);
}
QPushButton:hover {	/* 鼠标悬浮在QPushButton上时的状态 */
	background-color: rgb(255, 162, 56);
	/*color: #F2F2F2;*/
}

QPushButton:pressed { /* 鼠标按压在QPushButton上时的状态 */
	background-color: #c1c1c1;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="label_15">
     <property name="geometry">
      <rect>
       <x>226</x>
       <y>9</y>
       <width>501</width>
       <height>41</height>
      </rect>
     </property>
     <property name="text">
      <string>数据详情</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_16">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>70</y>
       <width>67</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>角度</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_abAngle_2">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>70</y>
       <width>131</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_17">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>70</y>
       <width>67</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>针</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_abNeedle_2">
     <property name="geometry">
      <rect>
       <x>300</x>
       <y>70</y>
       <width>131</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_current_speed_2">
     <property name="geometry">
      <rect>
       <x>580</x>
       <y>70</y>
       <width>91</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_18">
     <property name="geometry">
      <rect>
       <x>470</x>
       <y>70</y>
       <width>67</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>速度</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_set_speed_2">
     <property name="geometry">
      <rect>
       <x>680</x>
       <y>70</y>
       <width>91</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_selector">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>120</y>
       <width>211</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="MyLineEdit" name="label_fu_suo">
     <property name="geometry">
      <rect>
       <x>500</x>
       <y>120</y>
       <width>301</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_19">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>120</y>
       <width>67</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>梭子</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_20">
     <property name="geometry">
      <rect>
       <x>320</x>
       <y>120</y>
       <width>161</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>副梭纱嘴数据</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_21">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>170</y>
       <width>67</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>主梭</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_main_finger">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>170</y>
       <width>901</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="label_23">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>220</y>
       <width>67</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>气阀</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_valve">
     <property name="geometry">
      <rect>
       <x>90</x>
       <y>220</y>
       <width>901</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_info_reset">
     <property name="geometry">
      <rect>
       <x>720</x>
       <y>530</y>
       <width>89</width>
       <height>51</height>
      </rect>
     </property>
     <property name="text">
      <string>复位</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_info_next">
     <property name="geometry">
      <rect>
       <x>810</x>
       <y>530</y>
       <width>89</width>
       <height>51</height>
      </rect>
     </property>
     <property name="text">
      <string>下一圈</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pbtn_info_prev">
     <property name="geometry">
      <rect>
       <x>900</x>
       <y>530</y>
       <width>89</width>
       <height>51</height>
      </rect>
     </property>
     <property name="text">
      <string>上一圈</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_22">
     <property name="geometry">
      <rect>
       <x>820</x>
       <y>70</y>
       <width>67</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>圈号</string>
     </property>
    </widget>
    <widget class="MyLineEdit" name="le_curStepIdx_2">
     <property name="geometry">
      <rect>
       <x>890</x>
       <y>70</y>
       <width>101</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>0</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_24">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>270</y>
       <width>151</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>选针临时变更</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_needle_temp">
     <property name="geometry">
      <rect>
       <x>200</x>
       <y>270</y>
       <width>791</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="label_25">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>320</y>
       <width>121</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>电机</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_motor">
     <property name="geometry">
      <rect>
       <x>160</x>
       <y>320</y>
       <width>831</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>8</pointsize>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </widget>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyLineEdit</class>
   <extends>QLineEdit</extends>
   <header>CommonWidget/mylineedit.h</header>
  </customwidget>
  <customwidget>
   <class>ClickableLabel</class>
   <extends>QLabel</extends>
   <header>CommonWidget/clickablelabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
