#include "WorkForm.h"
#include "ui_WorkForm.h"

void WorkForm::initMainPage()
{
    if ( isMainPageInited == false )
    {
        // 通信相关
        connect( comm, &Communication::CommandFrameReceived, this, &WorkForm::onCommandReceived );

        // 文件解析相关
        sokBusiness = new SokBusiness( mainData, m_commOperations );
        // 所有step数据发送成功
        connect( sokBusiness, &SokBusiness::AllStepDataSendSuccess, this, &WorkForm::onAllStepDataSent );
        // SOK文件解析完成信号
        connect( sokBusiness, &SokBusiness::sokFileParseCompleted, this, &WorkForm::onSokFileParseCompleted );
        // CFT文件解析完成信号
        connect( sokBusiness, &SokBusiness::craftFileParseCompleted, this, &WorkForm::onCraftFileParseCompleted );

        // 建立sokfileSelectForm
        sokFileSelectForm = new FileSelectForm( nullptr, SOK_DIR, 0 );
        connect( sokFileSelectForm, &FileSelectForm::itemSelected, this, [&]( QString name ) {
            if ( name == currentSokFile )
                return;
            newSokFile = currentSokFile;
            // 触发SOK文件解析
            sokBusiness->readAndParseSokFile( name );
        } );

        // 控件事件
        connect( ui->le_sokFile, &MyLineEdit::mouseRelease, this, [&]() {
            if ( sokFileSelectForm != nullptr )
                sokFileSelectForm->show();
        } );
        connect( ui->le_size, &MyLineEdit::mouseRelease, this, [&]() {
            QMap< int, QString > sizeMap;
            sizeMap.insert( 0, "尺寸1" );
            sizeMap.insert( 1, "尺寸2" );
            sizeMap.insert( 2, "尺寸3" );
            sizeMap.insert( 3, "尺寸4" );
            sizeMap.insert( 4, "尺寸5" );
            sizeMap.insert( 5, "尺寸6" );
            sizeMap.insert( 6, "尺寸7" );
            sizeMap.insert( 7, "尺寸8" );
            comboForm = new ComboForm( nullptr, &sizeMap, 3 );
            comboForm->setAttribute( Qt::WA_DeleteOnClose );
            connect( comboForm, &ComboForm::itemSelected, this, &WorkForm::sizeSelected );
            comboForm->show();
        } );
        connect( ui->le_basNumSet, &MyLineEdit::mouseRelease, this, [&]() {
            numberInputForm = new NumberInputForm( nullptr, "请输入产量", 0, 60000 );
            numberInputForm->setAttribute( Qt::WA_DeleteOnClose );
            connect( numberInputForm, &NumberInputForm::InputFinished, this, &WorkForm::prodCounterSet );
            numberInputForm->show();
        } );
        cftFileSelectForm = new FileSelectForm( nullptr, CFT_DIR, 1 );
        connect( cftFileSelectForm, &FileSelectForm::itemSelected, this, [&]( QString name ) {
            if ( name == currentCraftFile )
                return;
            newCraftFile = currentCraftFile;
            // 触发CFT文件解析
            sokBusiness->readAndParseCftFile( name );
        } );
        connect( ui->le_craftFile, &MyLineEdit::mouseRelease, this, [&]() {
            if ( cftFileSelectForm == nullptr )
            {
                cftFileSelectForm->show();
            }
        } );
        //  默认显示
        ui->le_size->setText( QString( "尺寸%1" ).arg( sokBusiness->getCurrentSize() + 1 ) );
        ui->le_basNumSet->setText( QString::number( productNum ) );
        ui->lbl_machineName->setText( mainData->configManager->getMachineName() );
        // 机型来自MachineFileConfig
        ReadMachineFileConfig::MachineFlieConfigItem& item = ( *mainData->readMachineFileConfig->getBasicCfgList() )[ 1 ];
        ui->le_machineModel->setText( item.candidate[ item.value ] );
        // using Space as RunButton
        connect( ui->pbtn_Space, &QPushButton::clicked, this, &WorkForm::ToggleMachine );
        // using R as Return
        connect( ui->pbtn_R, &QPushButton::clicked, this, &WorkForm::onWorkFormHomeBtnClicked );
        // using Key as to WarnPage
        connect( ui->pbtn_Special, &QPushButton::clicked, this, [&]() {
            ui->stackedWidget->setCurrentIndex( 1 );
            refreshAllWarnWidgets();
        } );
        connect( ui->pbtn_Tab, &QPushButton::clicked, this, [&]() { ui->stackedWidget->setCurrentIndex( 2 ); } );

        // 功率相关弹窗
        // connect( ui->pbtn_F0, &QPushButton::clicked, this, [&]() {
        //     if ( powerDialog == nullptr )
        //     {
        //         //                powerDialog = new RunningPowerDialog();
        //         powerDialog = new RunningPowerDialog( this );
        //         // Set window flags to make it a top-level window
        //         powerDialog->setWindowFlags( Qt::Window | Qt::WindowStaysOnTopHint );
        //         // Set as non-modal
        //         powerDialog->setWindowModality( Qt::NonModal );
        //     }
        //     powerDialog->show();
        // } );
        connect( ui->pbtn_F0, &QPushButton::clicked, this, [&]() {
            QMessageBox::StandardButton reply;
            reply = QMessageBox::question( this, "确认", "程序链条将重新开始，是否确定？", QMessageBox::Yes | QMessageBox::No );
            if ( reply == QMessageBox::Yes )
            {
                // 只将第一圈数据调出来并显示，不发送。点击启动时，会重置当前运行步骤索引，并重发第一圈
                sokBusiness->resetCurrentRunningStepIdx();
                sokBusiness->getAndCheckNextCircle();
                refreshWorkFormInfo();
            }
        } );
        // 单件停车按钮
        connect( ui->pbtn_F3, &QPushButton::clicked, this, [&]() {
            bool isSingleStop = sokBusiness->getSingleStop();
            if ( isSingleStop )
            {
                sokBusiness->setSingleStop( false );
                ui->pbtn_F3->setStyleSheet( QString( "QPushButton { background-color: #27AE60; color: white; border-radius: 10px; border: none; }" ) );
            }
            else
            {
                sokBusiness->setSingleStop( true );
                ui->pbtn_F3->setStyleSheet( QString( "QPushButton { background-color: #EB3324; color: white; border-radius: 10px; border: none; }" ) );
            }
        } );
        // F4步段停车按钮
        connect( ui->pbtn_F4, &QPushButton::clicked, this, [&]() {
            bool isF4Stop = sokBusiness->getF4Stop();
            if ( isF4Stop )
            {
                sokBusiness->setF4Stop( false );
                ui->pbtn_F4->setStyleSheet( QString( "QPushButton { background-color: #27AE60; color: white; border-radius: 10px; border: none; }" ) );
            }
            else
            {
                sokBusiness->setF4Stop( true );
                ui->pbtn_F4->setStyleSheet( QString( "QPushButton { background-color: #EB3324; color: white; border-radius: 10px; border: none; }" ) );
            }
        } );
        // 低速锁定按钮
        connect( ui->pbtn_F5, &QPushButton::clicked, this, [&]() {
            bool isLowSpeedLock = sokBusiness->getLowSpeedLock();
            if ( isLowSpeedLock )
            {
                sokBusiness->setLowSpeedLock( false );
                ui->pbtn_F5->setStyleSheet( QString( "QPushButton { background-color: #27AE60; color: white; border-radius: 10px; border: none; }" ) );
            }
            else
            {
                sokBusiness->setLowSpeedLock( true );
                ui->pbtn_F5->setStyleSheet( QString( "QPushButton { background-color: #EB3324; color: white; border-radius: 10px; border: none; }" ) );
            }
        } );

        // 系统时间
        // 初始化系统时间定时器，每秒更新一次
        sysTickCounter = 0;                // 添加计数器变量
        sysTickTimer.setInterval( 1000 );  // 改为1秒触发一次
        sysTickTimer.start();
        connect( &sysTickTimer, &QTimer::timeout, this, [&]() {
            sysTickCounter++;  // 每秒增加计数器

            // 当计数到60时（即1分钟），更新界面上的时间标签
            if ( sysTickCounter >= 60 )
            {
                // 获取当前系统时间并格式化
                QString currentDate = QDateTime::currentDateTime().toString( "yyyy-MM-dd" );
                QString currentTime = QDateTime::currentDateTime().toString( "HH:mm" );
                // 更新界面上的时间标签
                ui->le_currentTime->setText( currentTime );
                ui->le_currentDate->setText( currentDate );

                // 重置计数器
                sysTickCounter = 0;
            }
            // 如果机器在运行状态,更新本件用时
            if ( mainData->machineState == MachineState::Running )
            {
                timeThisProdCounter++;  // 本件用时计数器加1秒
                // 计算分钟和秒数
                int minutes = timeThisProdCounter / 60;
                int seconds = timeThisProdCounter % 60;
                // 格式化显示为mm:ss
                QString timeStr = QString( "%1:%2" ).arg( minutes, 2, 10, QChar( '0' ) ).arg( seconds, 2, 10, QChar( '0' ) );
                ui->le_timeThisProd->setText( timeStr );
            }
        } );
        // 立即更新一次时间，不等待第一次定时器触发
        ui->le_currentDate->setText( QDateTime::currentDateTime().toString( "yyyy-MM-dd" ) );
        ui->le_currentTime->setText( QDateTime::currentDateTime().toString( "HH:mm" ) );
        // 获取IPv4地址
        // 获取本机所有网络接口
        QList< QNetworkInterface > interfaces = QNetworkInterface::allInterfaces();
        QString                    ipAddress;

        // 遍历所有网络接口
        for ( const QNetworkInterface& interface : interfaces )
        {
            // 跳过回环接口和非活动接口
            if ( interface.flags().testFlag( QNetworkInterface::IsLoopBack ) || !interface.flags().testFlag( QNetworkInterface::IsUp ) || !interface.flags().testFlag( QNetworkInterface::IsRunning ) )
            {
                continue;
            }

            // 获取接口的IP地址列表
            QList< QNetworkAddressEntry > entries = interface.addressEntries();
            for ( const QNetworkAddressEntry& entry : entries )
            {
                QHostAddress addr = entry.ip();
                // 只获取IPv4地址
                if ( addr.protocol() == QAbstractSocket::IPv4Protocol )
                {
                    ipAddress = addr.toString();
                    break;
                }
            }
            if ( !ipAddress.isEmpty() )
                break;
        }
        // 显示IP地址
        ui->le_netIp->setText( ipAddress );

        // 第一次进入页面时，读取ini配置文件，检查两个文件是否存在
        // 从ini配置文件读取上次使用的文件路径
        QString lastProgramFile = mainData->configManager->getLastProgramFile();
        QString lastCraftFile   = mainData->configManager->getLastCraftFile();

        // 检查sok文件是否存在
        if ( !lastProgramFile.isEmpty() && QFile::exists( lastProgramFile ) )
        {
            currentSokFile = lastProgramFile;
            // 只显示文件名,不显示路径
            QFileInfo fileInfo( lastProgramFile );
            QString   fileName = fileInfo.fileName();
            ui->le_sokFile->setText( fileName );
            // 设置sokBusiness的sokFile
            sokBusiness->setSokFile( lastProgramFile );
            // 触发SOK文件解析
            sokBusiness->readAndParseSokFile();
        }

        // 检查工艺文件是否存在
        if ( !lastCraftFile.isEmpty() && QFile::exists( lastCraftFile ) )
        {
            currentCraftFile = lastCraftFile;
            // 只显示文件名,不显示路径
            QFileInfo fileInfo( lastCraftFile );
            QString   fileName = fileInfo.fileName();
            ui->le_craftFile->setText( fileName );
            // 设置sokBusiness的cftFile
            sokBusiness->setCraftFile( lastCraftFile );
        }

        isMainPageInited = true;
    }
}

void WorkForm::ToggleMachine()
{
    if ( mainData->machineState == MachineState::Idle )
    {
        mainData->machineState = MachineState::Running;

        curProductNum = 0;
        ui->le_basNumCur->setText( "0" );
        // 重置本件用时
        timeThisProdCounter = 0;
        ui->le_timeThisProd->setText( "00:00" );
        // 重置step索引为0
        sokBusiness->resetCurrentRunningStepIdx();
        sokBusiness->getAndCheckNextCircle();
        // 设置"运行"标志
        sokBusiness->setControlFlag( 5, true );
        sokBusiness->sendNextStepData();
        refreshWorkFormInfo();
        ui->lbl_hintText->setText( "启动" );
    }
    else
    {
        mainData->machineState = MachineState::Idle;
        // 拿一圈数据，拿到的这个数据是第0圈的数据
        sokBusiness->getAndCheckNextCircle();
        // 设置"停止"标志
        sokBusiness->setControlFlag( 5, false );
        sokBusiness->sendNextStepData();
        // 重置step索引为0
        sokBusiness->resetCurrentRunningStepIdx();
    }
    // change Button Style
    if ( mainData->machineState == MachineState::Running )
        ui->pbtn_Space->setStyleSheet( QString( "QPushButton {"
                                                "background-color: #EB3324;"
                                                "border-radius:10px;"
                                                "color: white;"
                                                "}" ) );
    else if ( mainData->machineState == MachineState::Idle )
        ui->pbtn_Space->setStyleSheet( QString( "QPushButton {"
                                                "border-radius:10px;"
                                                "background-color: #27AE60;"
                                                "color: white;"
                                                "border : none;"
                                                "}" ) );
}

/* 返回主菜单 */
void WorkForm::onWorkFormHomeBtnClicked()
{
    this->hide();
    emit WorkFormToMainWinToShowSignal( 0 );
}

void WorkForm::onCommandReceived( qint16 size, quint8* data )
{
    if ( size > 1 )
    {
        // 第0字节第0位为1，说明要启动
        //        if ( ( data[ 0 ] & 0x01 ) && mainData->isMachineRunning == false )
        if ( data[ 0 ] & 0x01 )
        {
            ui->lbl_hintText->setText( "状态：启动" );
            //            runMachine();
        }
        else if ( data[ 0 ] & 0x02 )
        {
            ui->lbl_hintText->setText( "状态：点动" );
            //            stopMachine();
        }
        else if ( data[ 0 ] & 0x04 )
        {
            ui->lbl_hintText->setText( "状态：停止" );
        }
        else if ( data[ 0 ] & 0x08 )
        {
        }
        // 重发当前圈数据
        else if ( data[ 0 ] & 0x10 )
        {
            ui->lbl_hintText->setText( "重发当前圈数据" );
        }
        // 第0字节第1位为1，说明是下位向上位要数据
        else if ( data[ 0 ] & 0x20 )
        {
            // ui->lbl_hintText->setText( "发送下一圈数据" );
            if ( mainData->machineState == MachineState::Running )
            {
                // 先刷新页面数据，因为是提前1圈请求数据
                refreshWorkFormInfo();
                // 计算下一圈索引并发送
                sokBusiness->getAndCheckNextCircle();
                // 设置"运行"标志
                sokBusiness->setControlFlag( 5, true );
                sokBusiness->sendNextStepData();
            }
        }
        // 收到复位OK
        else if ( data[ 0 ] & 0x40 )
        {
            // 执行复位动作
        }
    }
}

void WorkForm::onAllStepDataSent()
{
    curProductNum++;
    ui->le_basNumCur->setText( QString::number( curProductNum ) );
    // 重置本件用时
    timeThisProdCounter = 0;
    ui->le_timeLastProd->setText( ui->le_timeThisProd->text() );
    ui->le_timeThisProd->setText( "00:00" );
    // Reach ProductNum
    if ( curProductNum >= productNum )
    {
        qDebug() << "Reach ProductNum";
        ui->lbl_hintText->setText( "达到预定产量" );
        ToggleMachine();
    }
}

void WorkForm::refreshWorkFormInfo()
{
    // 刷新当前运行的步骤索引
    ui->le_curStepIdx->setText( QString::number( sokBusiness->getCurrentRunningStepIdx() ) );
    // 找当前位于那个域 module
    SokBusiness::ModuleDataItem module = sokBusiness->getModuleInfo();
    ui->le_module->setText( module.moduleName );
    ui->le_module_minStep->setText( QString::number( module.startStep ) );
    ui->le_module_maxStep->setText( QString::number( module.endStep ) );
    // 当前step的SpeedSet，如果为0，则不显示，保留之前设置的速度值
    int speed = sokBusiness->getSpeedSet();
    if ( speed > 0 )
        ui->le_set_speed->setText( QString::number( speed ) );
    // Econonmics
    ui->le_economCur->setText( QString::number( sokBusiness->getCurrentEconomization() ) );
    ui->le_economSet->setText( QString::number( sokBusiness->getEconomizationSetting() ) );
    // 查找密度电机数据并显示
    ui->le_denstity1->setText( QString::number( sokBusiness->getDensityMotorSpeed( 0 ) ) );
    ui->le_denstity2->setText( QString::number( sokBusiness->getDensityMotorSpeed( 1 ) ) );
    ui->le_denstity3->setText( QString::number( sokBusiness->getDensityMotorSpeed( 2 ) ) );
}

void WorkForm::sizeSelected( int size )
{
    //    qDebug() << size;
    sokBusiness->setCurrentSize( size );
    ui->le_size->setText( QString( "尺寸%1" ).arg( size + 1 ) );
}

void WorkForm::prodCounterSet( QString text )
{
    productNum = text.toUShort();
    //    qDebug() << productNum;
    ui->le_basNumSet->setText( text );
}

void WorkForm::refreshWidgetFromUpperMsg( float abAngle, quint16 abNeedle, qint16 realSpeed )
{
    if ( ui->stackedWidget->currentIndex() == 0 )
    {
        ui->le_abAngle->setText( QString::number( abAngle, 'f', 1 ) );
        ui->le_abNeedle->setText( QString::number( abNeedle ) );
        ui->le_current_speed->setText( QString::number( realSpeed ) );
    }
    else if ( ui->stackedWidget->currentIndex() == 2 )
    {
        ui->le_abAngle_2->setText( QString::number( abAngle, 'f', 1 ) );
        ui->le_abNeedle_2->setText( QString::number( abNeedle ) );
        ui->le_current_speed_2->setText( QString::number( realSpeed ) );
    }
}

void WorkForm::onSokFileParseCompleted( bool success, const QString& errorMessage )
{
    if ( success )
    {
        // 解析成功，显示成功消息
        QMessageBox::information( this, "SOK文件解析", "SOK文件解析成功！" );

        currentSokFile = newSokFile;
        // 只显示文件名,不显示路径
        QFileInfo fileInfo( newSokFile );
        QString   fileName = fileInfo.fileName();
        ui->le_sokFile->setText( fileName );
        // 保存地址到ini文件
        mainData->configManager->setLastProgramFile( newSokFile );

        // 更新最大步骤索引显示
        ui->le_maxStepIdx->setText( QString::number( sokBusiness->getMaxStepIndex() ) );
    }
    else
    {
        // 解析失败，显示错误消息
        QString title   = "SOK文件解析失败";
        QString message = QString( "解析SOK文件时发生错误：\n\n%1\n\n请检查文件是否损坏或格式不正确。" ).arg( errorMessage );
        QMessageBox::critical( this, title, message );

        // 清空相关显示
        ui->le_maxStepIdx->setText( "0" );
    }
}

void WorkForm::onCraftFileParseCompleted( bool success, const QString& errorMessage )
{
    if ( success )
    {
        // 解析成功，显示成功消息
        QMessageBox::information( this, "工艺文件解析", "工艺文件解析成功！" );

        currentCraftFile = newCraftFile;
        // 只显示文件名,不显示路径
        QFileInfo fileInfo( newCraftFile );
        QString   fileName = fileInfo.fileName();
        ui->le_craftFile->setText( fileName );
        // 保存地址到ini文件
        mainData->configManager->setLastCraftFile( newCraftFile );
    } 
    else
    {
        // 解析失败，显示错误消息
        QString title   = "工艺文件解析失败";
        QString message = QString( "解析工艺文件时发生错误：\n\n%1\n\n请检查文件是否损坏或格式不正确。" ).arg( errorMessage );
        QMessageBox::critical( this, title, message );
    }
}